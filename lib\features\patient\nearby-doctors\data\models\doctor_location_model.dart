import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_location.dart';

class DoctorLocationModel extends DoctorLocation {
  const DoctorLocationModel({
    String id = '',
    required double latitude,
    required double longitude,
    required String address,
    required String city,
    required String state,
    required String country,
    required String zipCode,
    String name = '',
    double distance = 0.0,
  }) : super(
         id: id,
         latitude: latitude,
         longitude: longitude,
         address: address,
         city: city,
         state: state,
         country: country,
         zipCode: zipCode,
         name: name,
         distance: distance,
       );

  factory DoctorLocationModel.fromJson(Map<String, dynamic> json) {
    return DoctorLocationModel(
      id: json['id'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      country: json['country'] ?? '',
      zipCode: json['zipCode'] ?? '',
      name: json['name'] ?? '',
      distance: (json['distance'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'zipCode': zipCode,
      'name': name,
      'distance': distance,
    };
  }
}
