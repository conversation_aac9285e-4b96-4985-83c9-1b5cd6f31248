import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';

import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/bloc/doctor_bloc.dart'; // Assuming state is needed
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_info_card.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_profile_card.dart';

class DoctorFoundResultsWidget extends StatelessWidget {
  final List<Doctor> doctors;
  final DoctorState doctorState; // To show loading/empty states if needed
  final Function(Doctor) onDoctorTap; // Callback when a doctor card is tapped

  const DoctorFoundResultsWidget({
    super.key,
    required this.doctors,
    required this.doctorState,
    required this.onDoctorTap,
  });

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.15,
      maxChildSize: 0.6,
      builder: (BuildContext context, ScrollController scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16.0),
              topRight: Radius.circular(16.0),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 0,
                blurRadius: 5,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                height: 5,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Doctors found",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child:
                    (doctorState is NearbyDoctorsLoaded && doctors.isNotEmpty)
                        ? ListView.builder(
                          controller: scrollController,
                          itemCount: doctors.length,
                          itemBuilder: (context, index) {
                            final doctor = doctors[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 4.0,
                              ),
                              child: DoctorInfoCard(
                                doctor: doctor,
                                onTap: () => onDoctorTap(doctor),
                              ),
                            );
                          },
                        )
                        : (doctorState is NearbyDoctorsLoaded &&
                            doctors.isEmpty)
                        ? const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Text(
                              "No doctors found nearby. Try adjusting your filters.",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        )
                        : (doctorState is DoctorLoading)
                        ? const Center(child: CircularProgressIndicator())
                        : const SizedBox.shrink(),
              ),
              if (doctorState is NearbyDoctorsLoaded && doctors.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: CustomElevatedButton(
                    onPressed: () {
                      context.go(
                        AppRouter.doctorProfileView,
                        extra: doctors.first,
                      ); // Navigate to the first doctor's profile
                    },
                    text: 'View All',
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
