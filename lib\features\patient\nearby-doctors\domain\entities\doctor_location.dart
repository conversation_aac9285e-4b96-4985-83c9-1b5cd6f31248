import 'package:equatable/equatable.dart';

class DoctorLocation extends Equatable {
  final String id;
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String country;
  final String zipCode;
  final String name;
  final double distance;

  const DoctorLocation({
    this.id = '',
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.zipCode,
    this.name = '',
    this.distance = 0.0,
  });

  @override
  List<Object?> get props => [
    id,
    latitude,
    longitude,
    address,
    city,
    state,
    country,
    zipCode,
    name,
    distance,
  ];
}
