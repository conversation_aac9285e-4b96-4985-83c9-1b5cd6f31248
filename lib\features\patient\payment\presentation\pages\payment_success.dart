import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/infotile.dart';

class PaymentSuccess extends StatelessWidget {
  const PaymentSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: AppConstants.primaryColor,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: AppConstants.primaryColor,
      appBar: ReusableAppBar(
        title: Container(),
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
            color: Colors.white,
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            _SuccessMessage(),
            _ConsultationInfoCard(),
            _NavigationButtons(),
          ],
        ),
      ),
    );
  }
}

class _SuccessMessage extends StatelessWidget {
  const _SuccessMessage();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ReusableSvgImage(assetPath: "assets/icons/patient/success_icon.svg"),
          SizedBox(height: AppConstants.sizedBoxHeightMedium),
          Text(
            "Great !",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          Text(
            "Your payment is successfull",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class _ConsultationInfoCard extends StatelessWidget {
  const _ConsultationInfoCard();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Column(
        children: [
          Text(
            "Your consultation info",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightMedium),
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusSmall,
              ),
              border: Border.all(color: AppConstants.secondaryColor, width: 2),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: InfoTile(
                        showIcon: true,
                        iconPath: 'assets/icons/patient/calendar.svg',
                        title: "Consultation Fee",
                        subtitle: "45 AED",
                        iconColor: Colors.white,
                        titleColor: Colors.white,
                        subtitleColor: Colors.white,
                      ),
                    ),
                    Expanded(
                      child: InfoTile(
                        showIcon: false,
                        title: "12:30 PM",
                        subtitle: "28 April, Monday",
                        titleColor: Colors.white,
                        subtitleColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Row(
                  children: [
                    Expanded(
                      child: InfoTile(
                        showIcon: true,
                        iconPath: 'assets/icons/patient/ticket_icon.svg',
                        title: "Booking ID",
                        subtitle: "12131415",
                        iconColor: Colors.white,
                        titleColor: Colors.white,
                        subtitleColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _NavigationButtons extends StatelessWidget {
  const _NavigationButtons();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          CustomOutlinedButton(
            text: "Back to dashboard",
            width: double.infinity,
            borderRadius: AppConstants.borderRadiusSmall,
            fontWeight: FontWeight.w700,
            foregroundColor: Colors.white,
            borderColor: Colors.white,
            onPressed: () {
              context.go(AppRouter.register);
            },
          ),
        ],
      ),
    );
  }
}
