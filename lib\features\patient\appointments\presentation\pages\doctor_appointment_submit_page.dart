import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/custom_attachment_box.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/custom_input_box.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/custom_combo_box.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/doctor_card_basic.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/infotile.dart';

import 'package:imed_fe/core/router/app_router.dart';

class DoctorAppointmentSubmitPage extends StatelessWidget {
  const DoctorAppointmentSubmitPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
        title: Text(
          "Problem identification",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                  ),
                  child: Column(
                    children: [
                      DoctorCardBasic(
                        name: "Dr Rhonda Rhodes",
                        specialty: "Family Doctor",
                        degree: "MBBS FCPS",
                        imageUrl: "assets/images/person1.png",
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      _ConsultationInfoTile(),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      _FilterBox(),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      _ProfileInfo(name: "Nasir Hossein", gender: "Male"),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      CustomInputBox(
                        labelText: "Age",
                        hintText: "Your current age",
                        iconPath: "assets/icons/patient/calendar.svg",
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      CustomComboBox(
                        labelText: "Main complaint",
                        hintText: "Select complaint",
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      CustomInputBox(
                        labelText: "Describe your problem",
                        hintText: "Problem description",
                        maxLines: 5,
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                      CustomAttachmentBox(),
                      const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                border: const Border(
                  top: BorderSide(
                    color: Color(0xFFE6E7EB),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                ),
              ),
              child: Column(
                children: [
                  CustomElevatedButton(
                    text: "Submit now",
                    fontWeight: FontWeight.w700,
                    borderRadius: AppConstants.borderRadiusSmall,
                    backgroundColor: AppConstants.primaryColor,
                    onPressed: () {
                      context.go(AppRouter.appointmentSetwithBookingID);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ConsultationInfoTile extends StatelessWidget {
  const _ConsultationInfoTile();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.inputFieldBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: InfoTile(
                  showIcon: true,
                  iconPath: 'assets/icons/patient/calendar.svg',
                  title: "Consultation Fee",
                  subtitle: "45 AED",
                  iconColor: AppConstants.primaryColor,
                  titleColor: AppConstants.textMidColor,
                  subtitleColor: AppConstants.textHighColor,
                ),
              ),
              Expanded(
                child: InfoTile(
                  showIcon: false,
                  title: "12:30 PM",
                  subtitle: "28 April, Monday",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _FilterBox extends StatelessWidget {
  const _FilterBox();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            "Patient details",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w400,
              color: AppConstants.textMidColor,
            ),
          ),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CustomOutlinedButton(
              text: 'For myself',
              borderColor: AppConstants.primaryColor,
              foregroundColor: AppConstants.primaryColor,
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(text: 'Another person'),
          ],
        ),
      ],
    );
  }
}

class _ProfileInfo extends StatelessWidget {
  final String name;
  final String gender;
  const _ProfileInfo({required this.name, required this.gender});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          'assets/images/profile_patient.png',
          width: 64,
          height: 64,
          fit: BoxFit.cover,
        ),
        const SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppConstants.textHighColor,
              ),
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Text(
              gender,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppConstants.textMidColor,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
