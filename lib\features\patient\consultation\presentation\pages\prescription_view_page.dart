import 'package:flutter/material.dart';

import '../../../../../core/constants/app_constants.dart';

class PrescriptionViewPage extends StatelessWidget {
  const PrescriptionViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: <PERSON><PERSON>(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    child: Image.asset(
                      'assets/images/prescription.png',
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: 200,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: AppConstants.buttonHeight,
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(AppConstants.borderRadiusSmall),
                          bottomRight: Radius.circular(AppConstants.borderRadiusSmall),
                        ),
                      ),
                      alignment: Alignment.center,
                      child: const Text(
                        'TAP TO VIEW PRESCRIPTION',
                        style: TextStyle(
                          color: AppConstants.elevatedButtonForegroundColor,
                          fontWeight: FontWeight.bold,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            const Divider(),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: AppConstants.paddingSmall + 4), // 12.0
              child: Text(
                'Next Followup date',
                style: TextStyle(fontSize: AppConstants.fontSizeMedium, color: AppConstants.textMidColor),
              ),
            ),
            const Text(
              '3 Dec 2022',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightMedium),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              child: OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppConstants.primaryColor),
                  minimumSize: const Size.fromHeight(AppConstants.buttonHeight),
                ),
                child: const Text(
                  'Save',
                  style: TextStyle(color: AppConstants.primaryColor),
                ),
              ),
            ),
            const Spacer(),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium, vertical: AppConstants.paddingSmall),
                  child: Text(
                    'Note : you can download this prescription later from More > My prescriptions',
                    style: TextStyle(fontSize: AppConstants.fontSizeSmall, color: AppConstants.textMidColor),
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium, vertical: AppConstants.paddingSmall),
                  child: SizedBox(
                    width: AppConstants.buttonWidth,
                    height: AppConstants.buttonHeight,
                    child: ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.elevatedButtonBackgroundColor,
                      ),
                      child: const Text(
                        'Proceed Next',
                        style: TextStyle(color: AppConstants.elevatedButtonForegroundColor),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}