import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/widgets/reusable_svg_image.dart';
import '../widgets/keep_in_mind_section.dart';

class DoctorConnectionPage extends StatelessWidget {
  const DoctorConnectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppConstants.backgroundColor,
        title: const Text(
          'Connecting with doctor',
          style: TextStyle(color: AppConstants.textPrimaryColor),
        ),
        actions: [
          IconButton(
            icon: const ReusableSvgImage(
              assetPath: 'assets/icons/message_icon.svg',
              width: AppConstants.fontSizeExtraLarge,
              height: AppConstants.fontSizeExtraLarge,
              color: AppConstants.textPrimaryColor,
            ),
            onPressed: () {},
          ),
          SizedBox(width: AppConstants.sizedBoxHeightSmall),
        ],
      ),
      body: Column(
        children: [
          SizedBox(
            height:
                AppConstants.sizedBoxHeightExtraLarge +
                AppConstants.sizedBoxHeightMedium,
          ),

          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 350,
                  height: 350,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.05),
                    shape: BoxShape.circle,
                  ),
                ),
                Container(
                  width: 280,
                  height: 280,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.08),
                    shape: BoxShape.circle,
                  ),
                ),
                Container(
                  width: 210,
                  height: 210,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.12),
                    shape: BoxShape.circle,
                  ),
                ),
                GestureDetector(
                  onTap: () => context.push(AppRouter.doctorPatientCalling),
                  child: CircleAvatar(
                    radius: 70,
                    backgroundColor: AppConstants.inputFieldBackgroundColor,
                    backgroundImage: const AssetImage(
                      'assets/images/doctor_pic.png',
                    ),
                  ),
                ),
              ],
            ),
          ),

          Spacer(),

          KeepInMIndSection(),
          SizedBox(height: AppConstants.sizedBoxHeightMedium + 4),
        ],
      ),
    );
  }
}
