import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/rating.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/infotile.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/doctor_card_basic.dart';

class DoctorAppointmentBookingPage extends StatelessWidget {
  const DoctorAppointmentBookingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
        title: Text(
          "Appointment Set",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _DoctorInfo(),
                      SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
                      Image.asset(
                        'assets/images/robot_image.png',
                        fit: BoxFit.contain,
                        height: 270,
                      ),
                      const SizedBox(
                        height: AppConstants.sizedBoxHeightExtraLarge,
                      ),
                      _ConsultationInfo(),
                    ],
                  ),
                ),
              ),
            ),
            _BottomBar(),
          ],
        ),
      ),
    );
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: const Border(
          top: BorderSide(
            color: Color(0xFFE6E7EB),
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomElevatedButton(
              backgroundColor: AppConstants.primaryColor,
              text: "Confirm Booking",
              fontWeight: FontWeight.w700,
              borderRadius: AppConstants.borderRadiusSmall,
              prefixIcon: ReusableSvgImage(
                assetPath: 'assets/icons/patient/calendar.svg',
                color: Colors.white,
                width: 10.67,
                height: 12,
              ),
              onPressed: () {
                context.go(AppRouter.settingUpAppointment);
              },
            ),
          ),
          SizedBox(width: AppConstants.sizedBoxHeightMedium),
          Expanded(
            child: CustomOutlinedButton(
              text: "Cancel",
              fontWeight: FontWeight.w700,
              borderRadius: AppConstants.borderRadiusSmall,
              foregroundColor: AppConstants.primaryColor,
              borderColor: AppConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}

class _DoctorInfo extends StatelessWidget {
  const _DoctorInfo();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DoctorCardBasic(
          name: 'Dr Rohanda Rhodes',
          specialty: 'Family Doctor',
          degree: 'MBBS FCPS',
          imageUrl: 'assets/images/person1.png',
        ),
        SizedBox(height: AppConstants.sizedBoxHeightMedium),
        Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Experience",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: AppConstants.fontSizeSmall,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                Text(
                  "5 years+",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            SizedBox(width: AppConstants.sizedBoxHeightExtraLarge),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Rating",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: AppConstants.fontSizeSmall,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                Rating(rating: 4.8, reviewCount: 378, countAbout: "Reviews"),
              ],
            ),
          ],
        ),
        SizedBox(height: AppConstants.sizedBoxHeightMedium),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Serving in",
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                Row(
                  children: [
                    ReusableSvgImage(
                      assetPath: 'assets/icons/patient/hospital.svg',
                    ),
                    SizedBox(width: AppConstants.sizedBoxHeightSmall),
                    Text(
                      "NMC Speciality Hospital Abu Dhabi",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                        color: AppConstants.textMidColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class _ConsultationInfo extends StatelessWidget {
  const _ConsultationInfo();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFFEEF5FF),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        children: [
          Expanded(
            child: InfoTile(
              showIcon: true,
              iconPath: 'assets/icons/patient/calendar.svg',

              title: "Consultation Fee",
              subtitle: "45 AED",
              iconColor: AppConstants.primaryColor,
            ),
          ),
          Expanded(
            child: InfoTile(
              showIcon: false,
              title: "12:30 PM",
              subtitle: "28 April, Monday",
            ),
          ),
        ],
      ),
    );
  }
}
