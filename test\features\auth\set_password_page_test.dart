import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:imed_fe/features/auth/presentation/pages/set_password_page.dart';

void main() {
  testWidgets('SetPasswordPage shows fields and toggles visibility', (
    tester,
  ) async {
    await tester.pumpWidget(
      const MaterialApp(home: SetPasswordPage(email: '<EMAIL>')),
    );

    expect(find.text('Password'), findsWidgets);
    expect(find.text('Confirm'), findsOneWidget);

    // initial visibility icons
    expect(find.byIcon(Icons.visibility_off), findsWidgets);

    // tap first visibility off and expect icon to toggle for that field
    await tester.tap(find.byIcon(Icons.visibility_off).first);
    await tester.pumpAndSettle();

    expect(find.byIcon(Icons.visibility), findsWidgets);
  });
}
