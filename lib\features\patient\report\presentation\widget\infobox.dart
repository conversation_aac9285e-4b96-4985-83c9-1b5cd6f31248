import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class InfoBox extends StatelessWidget {
  final String title;
  final Widget content;
  const InfoBox({super.key, required this.title, required this.content});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            fontSize: 14,
            color: AppConstants.textMidColor,
          ),
        ),
        const SizedBox(height: 4),
        content,
      ],
    );
  }
}
