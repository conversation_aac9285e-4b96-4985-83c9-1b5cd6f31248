import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';

class TimeSlotModel extends TimeSlot {
  const TimeSlotModel({
    required String startTime,
    required String endTime,
    bool isAvailable = true,
  }) : super(startTime: startTime, endTime: endTime, isAvailable: isAvailable);

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    return TimeSlotModel(
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'isAvailable': isAvailable,
    };
  }
}

class DayScheduleModel extends DaySchedule {
  const DayScheduleModel({
    required String day,
    bool isAvailable = true,
    required List<TimeSlotModel> timeSlots,
  }) : super(day: day, isAvailable: isAvailable, timeSlots: timeSlots);

  factory DayScheduleModel.fromJson(Map<String, dynamic> json) {
    return DayScheduleModel(
      day: json['day'] ?? '',
      isAvailable: json['isAvailable'] ?? true,
      timeSlots:
          (json['timeSlots'] as List?)
              ?.map((slot) => TimeSlotModel.fromJson(slot))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'isAvailable': isAvailable,
      'timeSlots':
          (timeSlots as List<TimeSlotModel>)
              .map((slot) => slot.toJson())
              .toList(),
    };
  }
}

class DoctorAvailabilityModel extends DoctorAvailability {
  const DoctorAvailabilityModel({required List<DayScheduleModel> schedule})
    : super(schedule: schedule);

  factory DoctorAvailabilityModel.fromJson(Map<String, dynamic> json) {
    return DoctorAvailabilityModel(
      schedule:
          (json['schedule'] as List?)
              ?.map((day) => DayScheduleModel.fromJson(day))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'schedule':
          (schedule as List<DayScheduleModel>)
              .map((day) => day.toJson())
              .toList(),
    };
  }
}
