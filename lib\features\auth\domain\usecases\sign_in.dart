import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/entities/auth_credentials.dart';
import 'package:imed_fe/features/auth/domain/entities/user.dart';
import 'package:imed_fe/features/auth/domain/repositories/auth_repository.dart';

class SignIn implements UseCase<User, AuthCredentials> {
  final AuthRepository repository;

  SignIn(this.repository);

  @override
  Future<Either<Failure, User>> call(AuthCredentials params) async {
    return await repository.signIn(params);
  }
}