import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/infotile.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/doctor_card_basic.dart';

class DoctorAppointmentWithBookingIdPage extends StatelessWidget {
  const DoctorAppointmentWithBookingIdPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
        title: Text(
          "Appointment Set",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        trailing: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/bell_icon.svg',
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      DoctorCardBasic(
                        name: 'Dr Rohanda Rhodes',
                        specialty: 'Family Doctor',
                        degree: 'MBBS FCPS',
                        imageUrl: 'assets/images/person1.png',
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/robot_image.png',
                            fit: BoxFit.contain,
                            height: 187,
                          ),
                          const SizedBox(
                            height: AppConstants.sizedBoxHeightMedium,
                          ),
                          Text(
                            "Congratulation",
                            style: Theme.of(
                              context,
                            ).textTheme.bodyLarge?.copyWith(
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                              color: AppConstants.textHighColor,
                            ),
                          ),
                          const SizedBox(
                            height: AppConstants.sizedBoxHeightSmall,
                          ),
                          Text(
                            "Your booking is confirmed",
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: AppConstants.sizedBoxHeightExtraLarge,
                      ),
                      Container(
                        padding: const EdgeInsets.all(
                          AppConstants.paddingMedium,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.inputFieldBackgroundColor,
                          borderRadius: BorderRadius.circular(
                            AppConstants.borderRadiusSmall,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: InfoTile(
                                    showIcon: true,
                                    iconPath:
                                        'assets/icons/patient/calendar.svg',
                                    title: "Consultation Fee",
                                    subtitle: "45 AED",
                                  ),
                                ),
                                Expanded(
                                  child: InfoTile(
                                    showIcon: false,
                                    title: "12:30 PM",
                                    subtitle: "28 April, Monday",
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: AppConstants.sizedBoxHeightMedium,
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: InfoTile(
                                    showIcon: true,
                                    iconPath:
                                        'assets/icons/patient/ticket_icon.svg',
                                    title: "Booking ID",
                                    subtitle: "12131415",
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                border: const Border(
                  top: BorderSide(
                    color: Color(0xFFE6E7EB),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                ),
              ),
              child: Column(
                children: [
                  CustomElevatedButton(
                    text: "Payment",
                    fontWeight: FontWeight.w700,
                    borderRadius: AppConstants.borderRadiusSmall,
                    onPressed: () {
                      context.push(AppRouter.payment);
                    },
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightMedium),
                  CustomOutlinedButton(
                    borderRadius: AppConstants.borderRadiusSmall,
                    fontWeight: FontWeight.w700,
                    width: double.infinity,
                    text: "Back",
                    prefixIcon: ReusableSvgImage(
                      assetPath: 'assets/icons/patient/backward_arrow_icon.svg',
                      color: AppConstants.primaryColor,
                    ),
                    borderColor: AppConstants.primaryColor,
                    foregroundColor: AppConstants.primaryColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
