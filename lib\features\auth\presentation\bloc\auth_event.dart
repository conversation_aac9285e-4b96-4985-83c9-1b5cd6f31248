part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class CheckAuthStatus extends AuthEvent {}

class SignInRequested extends AuthEvent {
  final AuthCredentials credentials;

  const SignInRequested({required this.credentials});

  @override
  List<Object> get props => [credentials];
}

class SignUpRequested extends AuthEvent {
  final RegistrationData registrationData;

  const SignUpRequested({required this.registrationData});

  @override
  List<Object> get props => [registrationData];
}

class SignOutRequested extends AuthEvent {}

class ResetPasswordRequested extends AuthEvent {
  final String email;

  const ResetPasswordRequested({required this.email});

  @override
  List<Object> get props => [email];
}

class ConfirmResetPasswordRequested extends AuthEvent {
  final String token;
  final String newPassword;

  const ConfirmResetPasswordRequested({
    required this.token,
    required this.newPassword,
  });

  @override
  List<Object> get props => [token, newPassword];
}