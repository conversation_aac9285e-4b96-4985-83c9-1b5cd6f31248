import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Utility class to generate doctor markers for the map
class DoctorMarkerGenerator {
  static BitmapDescriptor? _cachedMarkerIcon;

  /// Generate a set of doctor markers around a central location
  static Future<Set<Marker>> generateDoctorMarkers(
    LatLng centerLocation,
  ) async {
    // Move positions list outside try-catch
    final dummyPositions = [
      LatLng(centerLocation.latitude + 0.01, centerLocation.longitude - 0.01),
      LatLng(centerLocation.latitude - 0.01, centerLocation.longitude + 0.01),
      LatLng(centerLocation.latitude + 0.005, centerLocation.longitude + 0.01),
      LatLng(centerLocation.latitude - 0.008, centerLocation.longitude - 0.005),
      LatLng(centerLocation.latitude + 0.003, centerLocation.longitude - 0.008),
      LatLng(centerLocation.latitude - 0.005, centerLocation.longitude + 0.005),
      LatLng(centerLocation.latitude + 0.007, centerLocation.longitude + 0.003),
      LatLng(centerLocation.latitude - 0.003, centerLocation.longitude - 0.007),
    ];

    try {
      // Initialize marker icon if not already cached
      if (_cachedMarkerIcon == null) {
        _cachedMarkerIcon = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(size: Size(48, 48)),
          'assets/icons/medical_shield_marker.png',
        );
      }

      final markers = dummyPositions.map((position) {
        return Marker(
          markerId: MarkerId('doctor_${position.latitude}_${position.longitude}'),
          position: position,
          icon: _cachedMarkerIcon ?? BitmapDescriptor.defaultMarker,
          infoWindow: const InfoWindow(title: 'Doctor'),
        );
      }).toSet();

      return markers;
    } catch (e) {
      debugPrint('Error generating markers: $e');
      // Fallback to default markers if custom icon fails
      return dummyPositions.map((position) {
        return Marker(
          markerId: MarkerId('doctor_${position.latitude}_${position.longitude}'),
          position: position,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'Doctor'),
        );
      }).toSet();
    }
  }

  /// Generate a user location marker
  static Marker generateUserMarker(LatLng userLocation) {
    return Marker(
      markerId: const MarkerId('currentLocation'),
      position: userLocation,
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      infoWindow: const InfoWindow(title: 'Your Location'),
    );
  }
}
