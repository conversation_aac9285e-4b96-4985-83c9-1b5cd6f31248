import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/repositories/auth_repository.dart';

class ResetPassword implements UseCase<void, String> {
  final AuthRepository repository;

  ResetPassword(this.repository);

  @override
  Future<Either<Failure, void>> call(String params) async {
    return await repository.resetPassword(params);
  }
}