import 'package:equatable/equatable.dart';

class PatientRegistrationRequest extends Equatable {
  final String roleId;
  final String firstName;
  final String lastName;
  final String phone;
  final String dateOfBirth;
  final String gender;

  const PatientRegistrationRequest({
    required this.roleId,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.dateOfBirth,
    required this.gender,
  });

  @override
  List<Object> get props => [
        roleId,
        firstName,
        lastName,
        phone,
        dateOfBirth,
        gender,
      ];
}
