import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ChatBotCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Widget? image;

  final BorderRadius? borderRadius;
  final Color backgroundColor;
  final List<BoxShadow>? boxShadow;

  const ChatBotCard({
    super.key,
    this.title = 'How are you feeling today?',
    this.subtitle = 'Hi, I\'ll be guiding you through',
    this.onTap,
    this.image,

    this.borderRadius,
    this.backgroundColor = Colors.white,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE0E3EA)),
          borderRadius: borderRadius ?? BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),

        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const Gap(4),
                Text(subtitle, style: const TextStyle(color: Colors.grey)),
              ],
            ),

            Image.asset('assets/icons/ai_assistant_icon.png', height: 110),
          ],
        ),
      ),
    );
  }
}
