import 'package:imed_fe/features/patient/profile/domain/entities/user_profile.dart';

class UserProfileModel extends UserProfile {
  const UserProfileModel({required String name, required String avatarUrl})
    : super(name: name, avatarUrl: avatarUrl);

  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      name: json['name'] ?? '',
      avatarUrl: json['avatar_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'avatar_url': avatarUrl};
  }
}
