import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/bed_time_countdown.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/chat_bot_card.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/doctor_card.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/quick_access_card.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/search_bar.dart';
import 'package:imed_fe/features/patient/profile/presentation/widgets/service_grid_item.dart';

class PatientProfilePage extends StatelessWidget {
  const PatientProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEFEFF),

      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                _buildHeaderSection(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                const CustomSearchBar(),
                const SizedBox(height: 25),
                _buildServiceGrid(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                const ChatBotCard(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                DoctorCard(
                  onTap: () => context.push(AppRouter.nearbyDoctorsSearch),
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Row(
                  children: [
                    Expanded(
                      child: QuickAccessCard(
                        iconAsset: 'assets/icons/labs_icon.svg',
                        shadowIconAsset: 'assets/icons/shadow_lab_icon.svg',
                        onTap: null,
                        title: 'Labs',
                        color: Color(0xFFeaedff),
                      ),
                    ),
                    SizedBox(width: AppConstants.sizedBoxHeightMedium),
                    Expanded(
                      child: QuickAccessCard(
                        iconAsset: 'assets/icons/pharmacy_icon.svg',
                        shadowIconAsset:
                            'assets/icons/shadow_pharmacy_icon.svg',
                        onTap: null,
                        title: 'Pharmacies',
                        color: Color(0xFFe4f4ff),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildHeaderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(
              'assets/images/profile_patient.png',
              height: 48,
              width: 48,
            ),

            ReusableSvgImage(
              assetPath: 'assets/icons/notification_icon.svg',
              height: 40,
              width: 40,
            ),
          ],
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightMedium),
        Text(
          'Welcome back',
          style: TextStyle(color: const Color(0xFF6C6E74), fontSize: 13),
        ),
        Text(
          'Nasir Hossain',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              "It's almost time for bed",
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            const BedTimeCountdown(),
          ],
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
      ],
    );
  }

  Widget _buildServiceGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      childAspectRatio: 2,
      children: const [
        ServiceGridItem(
          iconAsset: 'assets/icons/doctor_investigation_icon.svg',
          title: 'Doctors investigation',
          subtitle: 'All reports from your consultation',
          color: Color(0xFF2563EB),
        ),
        ServiceGridItem(
          iconAsset: 'assets/icons/medicine_icon.svg',
          title: 'Medicine',
          subtitle: 'Prescribed medicines from your reports',
          color: Color(0xFF06B6D4),
        ),
        ServiceGridItem(
          iconAsset: 'assets/icons/lab_report_icon.svg',
          title: 'Lab Reports',
          subtitle: 'Diagnosis reports from lab',
          color: Color(0xFF8B5CF6),
        ),
        ServiceGridItem(
          iconAsset: 'assets/icons/my_condition_icon.svg',
          title: 'My condition',
          subtitle: 'Check how your doing today',
          color: Color(0xFFEC4899),
        ),
      ],
    );
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF2563EB),
      unselectedItemColor: Colors.grey,
      currentIndex: 0,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.message), label: ''),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today_outlined),
          label: '',
        ),
      ],
    );
  }
}
