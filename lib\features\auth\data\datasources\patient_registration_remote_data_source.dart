import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_request_model.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_response_model.dart';

abstract class PatientRegistrationRemoteDataSource {
  Future<PatientRegistrationResponseModel> registerPatient(
    PatientRegistrationRequestModel request,
  );
}

class PatientRegistrationRemoteDataSourceImpl
    implements PatientRegistrationRemoteDataSource {
  final DioClient dioClient;

  PatientRegistrationRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<PatientRegistrationResponseModel> registerPatient(
    PatientRegistrationRequestModel request,
  ) async {
    try {
      // For now, return mock data since API is not ready
      // This will be replaced with actual API call later:
      // final response = await dioClient.post(
      //   '/patients/create-profile',
      //   data: request.toJson(),
      // );
      // return PatientRegistrationResponseModel.fromJson(response.data);

      // Mock successful response
      await Future.delayed(const Duration(seconds: 2)); // Simulate network delay
      
      return const PatientRegistrationResponseModel(
        id: 'f57b8c9e-8770-489d-8d6d-6f110c100e8d',
        message: 'Patient information created successfully',
        status: 'success',
        statusCode: 201,
      );
    } catch (e) {
      throw ServerException();
    }
  }
}
