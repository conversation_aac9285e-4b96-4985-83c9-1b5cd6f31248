part of 'patient_registration_bloc.dart';

abstract class PatientRegistrationEvent extends Equatable {
  const PatientRegistrationEvent();

  @override
  List<Object> get props => [];
}

class RegisterPatientRequested extends PatientRegistrationEvent {
  final PatientRegistrationRequest request;

  const RegisterPatientRequested({required this.request});

  @override
  List<Object> get props => [request];
}
