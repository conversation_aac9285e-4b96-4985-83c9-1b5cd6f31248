import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';

class DoctorAvailabilityCalendar extends StatefulWidget {
  final DoctorAvailability availability;
  final Function(String day, TimeSlot timeSlot)? onTimeSlotSelected;

  const DoctorAvailabilityCalendar({
    super.key,
    required this.availability,
    this.onTimeSlotSelected,
  });

  @override
  State<DoctorAvailabilityCalendar> createState() =>
      _DoctorAvailabilityCalendarState();
}

class _DoctorAvailabilityCalendarState
    extends State<DoctorAvailabilityCalendar> {
  String? selectedDay;
  TimeSlot? selectedTimeSlot;

  @override
  void initState() {
    super.initState();
    // Select the first available day by default
    for (final day in widget.availability.schedule) {
      if (day.isAvailable && day.timeSlots.isNotEmpty) {
        selectedDay = day.day;
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Available Days',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        const Gap(16),
        _buildDaySelector(),
        const Gap(24),
        if (selectedDay != null) ...[
          const Text(
            'Available Time Slots',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const Gap(16),
          _buildTimeSlots(),
        ],
      ],
    );
  }

  Widget _buildDaySelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children:
            widget.availability.schedule.map((day) {
              final isSelected = selectedDay == day.day;
              final isAvailable = day.isAvailable && day.timeSlots.isNotEmpty;

              return GestureDetector(
                onTap:
                    isAvailable
                        ? () {
                          setState(() {
                            selectedDay = day.day;
                            selectedTimeSlot = null;
                          });
                        }
                        : null,
                child: Container(
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? const Color(0xFF2563EB)
                            : isAvailable
                            ? Colors.white
                            : Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFF2563EB)
                              : isAvailable
                              ? Colors.grey[300]!
                              : Colors.grey[200]!,
                    ),
                  ),
                  child: Text(
                    day.day,
                    style: TextStyle(
                      color:
                          isSelected
                              ? Colors.white
                              : isAvailable
                              ? Colors.black
                              : Colors.grey,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTimeSlots() {
    final selectedDaySchedule = widget.availability.schedule.firstWhere(
      (day) => day.day == selectedDay,
    );

    if (!selectedDaySchedule.isAvailable ||
        selectedDaySchedule.timeSlots.isEmpty) {
      return const Text('No available time slots for this day');
    }

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          selectedDaySchedule.timeSlots.map((timeSlot) {
            final isSelected = selectedTimeSlot == timeSlot;
            final isAvailable = timeSlot.isAvailable;

            return GestureDetector(
              onTap:
                  isAvailable
                      ? () {
                        setState(() {
                          selectedTimeSlot = timeSlot;
                        });
                        if (widget.onTimeSlotSelected != null) {
                          widget.onTimeSlotSelected!(selectedDay!, timeSlot);
                        }
                      }
                      : null,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? const Color(0xFF2563EB)
                          : isAvailable
                          ? Colors.white
                          : Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        isSelected
                            ? const Color(0xFF2563EB)
                            : isAvailable
                            ? Colors.grey[300]!
                            : Colors.grey[200]!,
                  ),
                ),
                child: Text(
                  '${timeSlot.startTime} - ${timeSlot.endTime}',
                  style: TextStyle(
                    color:
                        isSelected
                            ? Colors.white
                            : isAvailable
                            ? Colors.black
                            : Colors.grey,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }
}
