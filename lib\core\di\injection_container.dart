import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/core/network/network_info.dart';
import 'package:imed_fe/core/network/network_info_impl.dart';
import 'package:imed_fe/features/auth/data/datasources/patient_registration_remote_data_source.dart';
import 'package:imed_fe/features/auth/data/repositories/patient_registration_repository_impl.dart';
import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';
import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';
import 'package:imed_fe/features/auth/presentation/bloc/patient_registration_bloc.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/datasources/doctor_remote_data_source.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/repositories/doctor_repository_impl.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/filter_doctors.dart'
    as filter_doctors;
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_availability.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_details.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_specialties.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_nearby_doctors.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/bloc/doctor_bloc.dart';

final sl = GetIt.instance;

void init() {
  // Blocs
  sl.registerFactory(
    () => DoctorBloc(
      getNearbyDoctors: sl(),
      getDoctorDetails: sl(),
      getDoctorAvailability: sl(),
      filterDoctorsUseCase: sl(),
      getDoctorSpecialties: sl(),
    ),
  );

  sl.registerFactory(() => PatientRegistrationBloc(registerPatient: sl()));

  // Use cases
  sl.registerLazySingleton(() => GetNearbyDoctors(sl()));
  sl.registerLazySingleton(() => GetDoctorDetails(sl()));
  sl.registerLazySingleton(() => GetDoctorAvailability(sl()));
  sl.registerLazySingleton(() => filter_doctors.FilterDoctors(sl()));
  sl.registerLazySingleton(() => GetDoctorSpecialties(sl()));
  sl.registerLazySingleton(() => RegisterPatient(sl()));

  // Repositories
  sl.registerLazySingleton<DoctorRepository>(
    () => DoctorRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<PatientRegistrationRepository>(
    () => PatientRegistrationRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<DoctorRemoteDataSource>(
    () => DoctorRemoteDataSourceImpl(dioClient: sl()),
  );

  sl.registerLazySingleton<PatientRegistrationRemoteDataSource>(
    () => PatientRegistrationRemoteDataSourceImpl(dioClient: sl()),
  );

  // Core
  sl.registerLazySingleton(() => DioClient());
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(InternetConnection()),
  );
}
