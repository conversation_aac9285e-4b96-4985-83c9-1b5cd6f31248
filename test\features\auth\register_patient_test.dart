import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';
import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';
import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';

import 'mocks.dart';

void main() {
  late MockPatientRegistrationRepository mockRepository;
  late RegisterPatient usecase;

  setUp(() {
    mockRepository = MockPatientRegistrationRepository();
    usecase = RegisterPatient(mockRepository);
  });

  setUpAll(() {
    // register fallback for mocktail matcher any() calls
    registerMocks();
  });

  final tRequest = PatientRegistrationRequest(
    roleId: 'role1',
    firstName: 'John',
    lastName: 'Doe',
    phone: '**********',
    dateOfBirth: '1990-01-01',
    gender: 'male',
  );

  final tResponse = PatientRegistrationResponse(
    id: 'id123',
    message: 'created',
    status: 'success',
    statusCode: 201,
  );

  test(
    'should return registration response when repository call is successful',
    () async {
      when(
        () => mockRepository.registerPatient(any()),
      ).thenAnswer((_) async => Right(tResponse));

      final result = await usecase(tRequest);

      expect(result, Right(tResponse));
      verify(() => mockRepository.registerPatient(tRequest));
      verifyNoMoreInteractions(mockRepository);
    },
  );

  test(
    'should return ServerFailure when repository throws server failure',
    () async {
      when(
        () => mockRepository.registerPatient(any()),
      ).thenAnswer((_) async => Left(ServerFailure()));

      final result = await usecase(tRequest);

      expect(result, Left(ServerFailure()));
      verify(() => mockRepository.registerPatient(tRequest));
      verifyNoMoreInteractions(mockRepository);
    },
  );
}
