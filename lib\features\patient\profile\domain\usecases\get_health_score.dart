import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/health_score.dart';
import 'package:imed_fe/features/patient/profile/domain/repositories/home_repository.dart';

class GetHealthScore implements UseCase<HealthScore, NoParams> {
  final HomeRepository repository;

  GetHealthScore(this.repository);

  @override
  Future<Either<Failure, HealthScore>> call(NoParams params) async {
    try {
      final result = await repository.getHealthScore();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
