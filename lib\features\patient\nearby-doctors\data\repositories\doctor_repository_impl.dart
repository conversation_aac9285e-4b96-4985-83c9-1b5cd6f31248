import 'package:imed_fe/features/patient/nearby-doctors/data/datasources/doctor_remote_data_source.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class DoctorRepositoryImpl implements DoctorRepository {
  final DoctorRemoteDataSource remoteDataSource;

  DoctorRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<Doctor>> getNearbyDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
  }) async {
    return await remoteDataSource.getNearbyDoctors(
      latitude: latitude,
      longitude: longitude,
      radius: radius,
      specialty: specialty,
    );
  }

  @override
  Future<Doctor> getDoctorDetails(String doctorId) async {
    return await remoteDataSource.getDoctorDetails(doctorId);
  }

  @override
  Future<DoctorAvailability> getDoctorAvailability(String doctorId) async {
    return await remoteDataSource.getDoctorAvailability(doctorId);
  }

  @override
  Future<List<Doctor>> filterDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
    double? minRating,
    bool? isAvailableForSchedule,
    String? searchQuery,
  }) async {
    return await remoteDataSource.filterDoctors(
      latitude: latitude,
      longitude: longitude,
      radius: radius,
      specialty: specialty,
      minRating: minRating,
      isAvailableForSchedule: isAvailableForSchedule,
      searchQuery: searchQuery,
    );
  }

  @override
  Future<List<String>> getDoctorSpecialties() async {
    return await remoteDataSource.getDoctorSpecialties();
  }
}
