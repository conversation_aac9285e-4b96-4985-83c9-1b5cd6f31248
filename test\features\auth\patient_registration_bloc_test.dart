import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';
import 'package:imed_fe/features/auth/presentation/bloc/patient_registration_bloc.dart';
import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';

import 'mocks.dart';

void main() {
  late MockRegisterPatient mockRegisterPatient;
  late PatientRegistrationBloc bloc;

  setUp(() {
    mockRegisterPatient = MockRegisterPatient();
    bloc = PatientRegistrationBloc(registerPatient: mockRegisterPatient);
    registerMocks();
  });

  final tRequest = PatientRegistrationRequest(
    roleId: 'role1',
    firstName: 'John',
    lastName: 'Doe',
    phone: '**********',
    dateOfBirth: '1990-01-01',
    gender: 'male',
  );

  final tResponse = PatientRegistrationResponse(
    id: 'id123',
    message: 'created',
    status: 'success',
    statusCode: 201,
  );

  test('initial state should be PatientRegistrationInitial', () {
    expect(bloc.state, isA<PatientRegistrationInitial>());
  });

  blocTest<PatientRegistrationBloc, PatientRegistrationState>(
    'emits [Loading, Success] when registration succeeds',
    build: () {
      when(
        () => mockRegisterPatient(any()),
      ).thenAnswer((_) async => Right(tResponse));
      return bloc;
    },
    act: (bloc) => bloc.add(RegisterPatientRequested(request: tRequest)),
    expect:
        () => [
          isA<PatientRegistrationLoading>(),
          isA<PatientRegistrationSuccess>(),
        ],
  );

  blocTest<PatientRegistrationBloc, PatientRegistrationState>(
    'emits [Loading, Error] when registration fails with server failure',
    build: () {
      when(
        () => mockRegisterPatient(any()),
      ).thenAnswer((_) async => Left(ServerFailure()));
      return bloc;
    },
    act: (bloc) => bloc.add(RegisterPatientRequested(request: tRequest)),
    expect:
        () => [
          isA<PatientRegistrationLoading>(),
          isA<PatientRegistrationError>(),
        ],
  );
}
