import 'package:mocktail/mocktail.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_request_model.dart';
import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';
import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';

class MockPatientRegistrationRepository extends Mock
    implements PatientRegistrationRepository {}

class MockRegisterPatient extends Mock implements RegisterPatient {}

class FakePatientRegistrationRequest extends Fake
    implements PatientRegistrationRequest {}

void registerMocks() {
  registerFallbackValue(FakePatientRegistrationRequest());
  registerFallbackValue(FakePatientRegistrationRequestModel());
}

class FakePatientRegistrationRequestModel extends Fake
    implements PatientRegistrationRequestModel {}
