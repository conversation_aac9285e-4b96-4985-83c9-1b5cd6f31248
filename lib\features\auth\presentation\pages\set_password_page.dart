import 'package:flutter/material.dart';

import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/utils/validation_utils.dart';

import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_text_field.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_icon_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class SetPasswordPage extends StatefulWidget {
  final String email;

  const SetPasswordPage({super.key, required this.email});

  @override
  State<SetPasswordPage> createState() => _SetPasswordPageState();
}

class _SetPasswordPageState extends State<SetPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }

  void _handleSetPassword() {
    if (_formKey.currentState!.validate()) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: ReusableSvgImage(assetPath: 'assets/icons/left.svg'),
        title: const Text('Set Password'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Password',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                  color: AppConstants.textMidColor,
                ),
              ),
              const SizedBox(height: AppConstants.sizedBoxHeightSmall),
              _buildPasswordField(),
              const SizedBox(height: AppConstants.sizedBoxHeightMedium),
              Text(
                'Confirm Password',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textMidColor,
                ),
              ),
              const SizedBox(height: AppConstants.sizedBoxHeightSmall),
              _buildConfirmPasswordField(),
              const SizedBox(height: AppConstants.sizedBoxHeightMedium),
              CustomElevatedButton(
                onPressed: _handleSetPassword,
                text: 'Confirm',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return CustomTextField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      hintText: 'Enter your password',
      validator: ValidationUtils.validatePassword,
      suffixIcon: ReusableIconButton(
        icon: _obscurePassword ? Icons.visibility_off : Icons.visibility,
        onPressed: _togglePasswordVisibility,
        color: AppConstants.textMidColor,
      ),
    );
  }

  Widget _buildConfirmPasswordField() {
    return CustomTextField(
      controller: _confirmPasswordController,
      obscureText: _obscureConfirmPassword,
      hintText: 'Confirm your password',
      validator:
          (value) => ValidationUtils.validateConfirmPassword(
            _passwordController.text,
            _confirmPasswordController.text,
          ),
      suffixIcon: ReusableIconButton(
        icon: _obscurePassword ? Icons.visibility_off : Icons.visibility,
        onPressed: _toggleConfirmPasswordVisibility,
        color: AppConstants.textMidColor,
      ),
    );
  }
}
