import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/user_profile.dart';
import 'package:imed_fe/features/patient/profile/domain/repositories/home_repository.dart';

class GetUserProfile implements UseCase<UserProfile, NoParams> {
  final HomeRepository repository;

  GetUserProfile(this.repository);

  @override
  Future<Either<Failure, UserProfile>> call(NoParams params) async {
    try {
      final result = await repository.getUserProfile();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
