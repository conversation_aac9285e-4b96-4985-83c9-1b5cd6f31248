import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/report/presentation/widget/infobox.dart';
import 'package:imed_fe/features/patient/report/presentation/widget/tags.dart';

class DoctorReportUploadedReportCaptured extends StatelessWidget {
  const DoctorReportUploadedReportCaptured({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        title: Text(
          "Upload report",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _DoctorInfo(
                      doctorName: "Doctor Name",
                      doctorDegree: "Degree 1, Degree 2, Degree 3",
                    ),
                    Divider(color: AppConstants.borderColor, thickness: 1),
                    _PatientInfo(
                      patientName: "Patient",
                      patientSex: "Male",
                      patientAge: "XX",
                      patientWeight: "XX",
                    ),
                    Divider(color: AppConstants.borderColor, thickness: 1),
                    _ChiefComplaints(),
                    Divider(color: AppConstants.borderColor, thickness: 1),
                    _FollowUp(),
                    Divider(color: AppConstants.borderColor, thickness: 1),
                    _AdviceList(),
                    Divider(color: AppConstants.borderColor, thickness: 1),
                  ],
                ),
              ),
            ),
            _BottomBar(),
          ],
        ),
      ),
    );
  }
}

class _DoctorInfo extends StatelessWidget {
  final String doctorName;
  final String doctorDegree;
  const _DoctorInfo({required this.doctorName, required this.doctorDegree});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Doctor Name",
            content: Text(
              doctorName,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Degree",
            content: Text(
              doctorDegree,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class _PatientInfo extends StatelessWidget {
  final String patientName;
  final String patientSex;
  final String patientAge;
  final String patientWeight;
  const _PatientInfo({
    required this.patientName,
    required this.patientSex,
    required this.patientAge,
    required this.patientWeight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Column(
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Patient Name",
            content: Row(
              children: [
                Text(
                  patientName,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
                ),
                SizedBox(width: AppConstants.sizedBoxHeightSmall),
                ReusableSvgImage(
                  assetPath: "assets/icons/patient/male_gender_icon.svg",
                ),
              ],
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Row(
            children: [
              Expanded(
                child: InfoBox(
                  title: "Age",
                  content: Text(
                    patientAge,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InfoBox(
                  title: "Weight",
                  content: Text(
                    patientWeight,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class _ChiefComplaints extends StatelessWidget {
  const _ChiefComplaints();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Chief Complaints",
            content: Wrap(
              runAlignment: WrapAlignment.start,
              spacing: 8,
              runSpacing: 8,
              children: [
                Tags(tagName: "Complain 01"),
                Tags(tagName: "Complain 02"),
                Tags(tagName: "Complain 03"),
                Tags(tagName: "Complain 04"),
              ],
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class _FollowUp extends StatelessWidget {
  const _FollowUp();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Follow Up",
            content: Wrap(children: [Tags(tagName: "Non")]),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class _AdviceList extends StatelessWidget {
  const _AdviceList();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          InfoBox(
            title: "Advice",
            content: Column(
              spacing: AppConstants.sizedBoxHeightSmall,
              children: [
                Text(
                  "Advice 01",
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
                ),
                Text(
                  "Advice 02",
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
                ),
                Text(
                  "Advice 03",
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
                ),
                Text(
                  "Advice 04",
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),

      child: Row(
        children: [
          Expanded(
            child: CustomOutlinedButton(
              text: "Edit",
              fontWeight: FontWeight.w700,
              borderRadius: AppConstants.borderRadiusSmall,
              foregroundColor: AppConstants.primaryColor,
              borderColor: AppConstants.primaryColor,
            ),
          ),
          SizedBox(width: AppConstants.sizedBoxHeightMedium),
          Expanded(
            child: CustomElevatedButton(
              backgroundColor: AppConstants.primaryColor,
              text: "Done",
              fontWeight: FontWeight.w700,
              borderRadius: AppConstants.borderRadiusSmall,
              prefixIcon: ReusableSvgImage(
                assetPath: 'assets/icons/patient/check_icon.svg',
                color: Colors.white,
                width: 10.67,
                height: 12,
              ),
              onPressed: () {
                context.push(AppRouter.doctorConnection);
              },
            ),
          ),
        ],
      ),
    );
  }
}
