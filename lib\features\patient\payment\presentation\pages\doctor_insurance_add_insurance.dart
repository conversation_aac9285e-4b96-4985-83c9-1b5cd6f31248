import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/custom_input_box.dart';

class DoctorInsuranceAddInsurance extends StatelessWidget {
  const DoctorInsuranceAddInsurance({super.key});

  @override
  Widget build(BuildContext context) {
    final insuranceCompanyList = [
      {
        "value": "metlife",
        "label": "Metlife",
        "iconPath": "assets/images/company3.png",
      },
    ];

    return Scaffold(
      appBar: ReusableAppBar(
        title: Text(
          "Add Insurance",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _SelectProviderDropdown(
                              icList: insuranceCompanyList,
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            CustomInputBox(
                              labelText: "Policy number   *",
                              hintText: "Sample : 000-00000",
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            CustomInputBox(
                              labelText: "Group number",
                              hintText: "Sample : 000-00000",
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            CustomInputBox(
                              labelText: "Validity date   *",
                              hintText: "Sample : 12-12-2027",
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            CustomInputBox(
                              labelText: "Policy holder name   *",
                              hintText: "Sample : Joe Rogan",
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            CustomInputBox(
                              labelText: "Date of birth",
                              hintText: "dd/mm/yyyy",
                            ),
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                            _UploadInsuranceCard(),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: AppConstants.sizedBoxHeightMedium),
                    _BottomBar(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SelectProviderDropdown extends StatelessWidget {
  final List icList;
  const _SelectProviderDropdown({required this.icList});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Provider',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppConstants.textMidColor,
            fontWeight: FontWeight.w400,
            fontSize: 12,
          ),
        ),
        DropdownButtonFormField(
          decoration: InputDecoration(
            filled: true,
            fillColor: AppConstants.backgroundColor,
            hintText: icList.isNotEmpty ? icList[0]['label'] : null,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusExtraLarge,
              ),
              borderSide: BorderSide(color: AppConstants.borderColor, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusExtraLarge,
              ),
              borderSide: BorderSide(color: AppConstants.borderColor, width: 2),
            ),
          ),
          items:
              icList
                  .map(
                    (item) => DropdownMenuItem(
                      value: item['value'],
                      child: Row(
                        children: [
                          Image.asset(item['iconPath']!, width: 48, height: 48),
                          SizedBox(width: 8),
                          Text(
                            item['label']!,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color: AppConstants.inputFieldForegroundColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
          value: icList.isNotEmpty ? icList[0]['value'] : null,
          selectedItemBuilder:
              (context) =>
                  icList
                      .map(
                        (item) => Row(
                          children: [
                            Image.asset(
                              item['iconPath']!,
                              width: 48,
                              height: 48,
                            ),
                            SizedBox(width: 8),
                            Text(
                              item['label']!,
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color: AppConstants.inputFieldForegroundColor,
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      )
                      .toList(),
          onChanged: (value) {},
        ),
      ],
    );
  }
}

class _UploadInsuranceCard extends StatelessWidget {
  const _UploadInsuranceCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 246,
      decoration: BoxDecoration(
        color: AppConstants.inputFieldBackgroundColor,
        border: Border.all(color: AppConstants.inputFieldForegroundColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ReusableSvgImage(
              assetPath: 'assets/icons/patient/plus_icon.svg',
              width: 32,
              height: 32,
            ),
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Text(
              'Upload insurance card',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.primaryColor,
                fontWeight: FontWeight.w700,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
      child: Column(
        children: [
          CustomElevatedButton(
            text: "Add Now",
            fontWeight: FontWeight.w700,
            borderRadius: AppConstants.borderRadiusSmall,
            onPressed: () {
              context.go(AppRouter.paymentSuccess);
            },
          ),
        ],
      ),
    );
  }
}
