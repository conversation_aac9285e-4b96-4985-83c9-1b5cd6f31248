import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/search_bar.dart';

class DoctorAppointmentNotifications extends StatelessWidget {
  const DoctorAppointmentNotifications({super.key});

  @override
  Widget build(BuildContext context) {
    List today = [
      {
        'title': 'Scheduled appointment',
        'subtitle': 'Donec dictum tristique porta. Etiam convallis lorem',
        'time': '12:20 PM',
        'imageUrl':
            'assets/icons/patient/doctor_investigation_notification_icon.svg',
      },
      {
        'title': 'Medicine appointment',
        'subtitle': 'Donec dictum tristique porta. Etiam convallis lorem',
        'time': '12:20 PM',
        'imageUrl': 'assets/icons/patient/medicine_notification_icon.svg',
      },
      {
        'title': 'Medicine appointment',
        'subtitle': 'Donec dictum tristique porta. Etiam convallis lorem',
        'time': '12:20 PM',
        'imageUrl': 'assets/icons/patient/medicine_notification_icon.svg',
      },
    ];

    List yesterday = [
      {
        'title': 'Lab report',
        'subtitle': 'Donec dictum tristique porta. Etiam convallis lorem',
        'time': '12:20 PM',
        'imageUrl': 'assets/icons/patient/lab_report_notification_icon.svg',
      },
    ];

    return Scaffold(
      appBar: ReusableAppBar(
        title: Text(
          "Notification",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                ),
                child: Column(
                  children: [
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    CustomSearchBar(
                      onTap:
                          () => context.push(
                            AppRouter.prescriptionAndDiagnosticReport,
                          ),
                    ),
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    _FilterBox(),
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    Column(
                      children: [
                        _DaySection(day: "Today", list: today),
                        SizedBox(height: AppConstants.sizedBoxHeightMedium),
                        _DaySection(day: "Yesterday", list: yesterday),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FilterBox extends StatelessWidget {
  const _FilterBox();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.paddingSmall,
        ),
        child: Row(
          children: [
            CustomOutlinedButton(
              text: 'All',
              foregroundColor: AppConstants.primaryColor,
              borderColor: AppConstants.primaryColor,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(
              text: 'Doctor',
              foregroundColor: AppConstants.textMidColor,
              borderColor: AppConstants.borderColor,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(
              text: 'Medicine',
              foregroundColor: AppConstants.textMidColor,
              borderColor: AppConstants.borderColor,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(
              text: 'Lab',
              foregroundColor: AppConstants.textMidColor,
              borderColor: AppConstants.borderColor,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(
              text: 'Saved for later',
              foregroundColor: AppConstants.textMidColor,
              borderColor: AppConstants.borderColor,
            ),
          ],
        ),
      ),
    );
  }
}

class _DaySection extends StatelessWidget {
  final String day;
  final List list;
  const _DaySection({required this.day, required this.list});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            day,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
        ),
        SizedBox(height: AppConstants.sizedBoxHeightMedium),
        Column(
          spacing: 10,
          children: List.generate(
            list.length,
            (index) => _NotificationItem(
              title: list[index]['title'],
              subtitle: list[index]['subtitle'],
              time: list[index]['time'],
              imageUrl: list[index]['imageUrl'],
            ),
          ),
        ),
      ],
    );
  }
}

class _NotificationItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String time;
  final String imageUrl;
  const _NotificationItem({
    required this.title,
    required this.subtitle,
    required this.time,
    this.imageUrl = '',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ReusableSvgImage(assetPath: imageUrl, width: 40, height: 40),
        SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textMidColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  time,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
