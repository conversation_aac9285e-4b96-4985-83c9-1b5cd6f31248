import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_availability_model.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_location_model.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_model.dart';

abstract class DoctorRemoteDataSource {
  Future<List<DoctorModel>> getNearbyDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
  });

  Future<DoctorModel> getDoctorDetails(String doctorId);

  Future<DoctorAvailabilityModel> getDoctorAvailability(String doctorId);

  Future<List<DoctorModel>> filterDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
    double? minRating,
    bool? isAvailableForSchedule,
    String? searchQuery,
  });

  Future<List<String>> getDoctorSpecialties();
}

class DoctorRemoteDataSourceImpl implements DoctorRemoteDataSource {
  final DioClient dioClient;

  DoctorRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<DoctorModel>> getNearbyDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'latitude': latitude,
        'longitude': longitude,
        'radius': radius,
      };

      if (specialty != null) {
        queryParams['specialty'] = specialty;
        // The specialty is passed as a string parameter, not a double
        // This is fine as the API will handle it correctly
      }

      final response = await dioClient.get(
        '/doctors/nearby',
        queryParameters: queryParams,
      );

      return (response.data['doctors'] as List)
          .map((doctor) => DoctorModel.fromJson(doctor))
          .toList();
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return _getMockDoctors(latitude, longitude);
    }
  }

  @override
  Future<DoctorModel> getDoctorDetails(String doctorId) async {
    try {
      final response = await dioClient.get('/doctors/$doctorId');
      return DoctorModel.fromJson(response.data);
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return _getMockDoctors(23.8103, 90.4125).first;
    }
  }

  @override
  Future<DoctorAvailabilityModel> getDoctorAvailability(String doctorId) async {
    try {
      final response = await dioClient.get('/doctors/$doctorId/availability');
      return DoctorAvailabilityModel.fromJson(response.data);
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return _getMockAvailability();
    }
  }

  @override
  Future<List<DoctorModel>> filterDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
    double? minRating,
    bool? isAvailableForSchedule,
    String? searchQuery,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'latitude': latitude,
        'longitude': longitude,
        'radius': radius,
      };

      if (specialty != null) {
        queryParams['specialty'] = specialty;
      }

      if (minRating != null) {
        queryParams['minRating'] = minRating;
      }

      if (isAvailableForSchedule != null) {
        queryParams['isAvailableForSchedule'] = isAvailableForSchedule;
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['query'] = searchQuery;
      }

      final response = await dioClient.get(
        '/doctors/filter',
        queryParameters: queryParams,
      );

      return (response.data['doctors'] as List)
          .map((doctor) => DoctorModel.fromJson(doctor))
          .toList();
    } catch (e) {
      // For demo purposes, filter the mock data
      final doctors = _getMockDoctors(latitude, longitude);

      return doctors.where((doctor) {
        bool matches = true;

        if (specialty != null) {
          matches = matches && doctor.specialty == specialty;
        }

        if (minRating != null) {
          matches = matches && doctor.rating >= minRating;
        }

        if (isAvailableForSchedule != null) {
          matches =
              matches &&
              doctor.isAvailableForSchedule == isAvailableForSchedule;
        }

        if (searchQuery != null && searchQuery.isNotEmpty) {
          final query = searchQuery.toLowerCase();
          matches =
              matches &&
              (doctor.name.toLowerCase().contains(query) ||
                  doctor.specialty.toLowerCase().contains(query) ||
                  doctor.about.toLowerCase().contains(query) ||
                  doctor.hospitalName.toLowerCase().contains(query));
        }

        return matches;
      }).toList();
    }
  }

  @override
  Future<List<String>> getDoctorSpecialties() async {
    try {
      final response = await dioClient.get('/doctors/specialties');
      return List<String>.from(response.data['specialties']);
    } catch (e) {
      // For demo purposes, return mock specialties
      return [
        'Family Doctor',
        'Cardiologist',
        'Neurologist',
        'Pediatrician',
        'Dermatologist',
        'Gynecologist',
        'Orthopedic',
        'Psychiatrist',
        'Dentist',
        'Pulmonologist',
        'Gastroenterologist',
        'Optomologist',
      ];
    }
  }

  // Mock data for development purposes
  List<DoctorModel> _getMockDoctors(double latitude, double longitude) {
    return [
      DoctorModel(
        id: '1',
        name: 'Dr. Anika Rahman',
        specialty: 'Cardiologist',
        imageUrl: 'assets/images/doctor1.png',
        rating: 4.8,
        reviewCount: 124,
        about:
            'Dr. Anika Rahman is a board-certified cardiologist with over 10 years of experience in treating heart diseases.',
        phoneNumber: '+8801712345678',
        email: '<EMAIL>',
        location: DoctorLocationModel(
          id: '1',
          latitude: latitude + 0.01,
          longitude: longitude - 0.01,
          address: '123 Medical Center',
          city: 'Dhaka',
          state: 'Dhaka',
          country: 'Bangladesh',
          zipCode: '1000',
          name: 'Medical Center',
          distance: 0.9,
        ),
        availability: _getMockAvailability(),
        consultationFee: 1500,
        services: [
          'Cardiac Consultation',
          'ECG',
          'Echocardiography',
          'Stress Test',
        ],
        languages: ['Bengali', 'English'],
        education: 'MBBS, FCPS (Cardiology), MRCP (UK)',
        experience: '10+ years in cardiology',
        hospitalName: 'NMC Specialty Hospital Abu Dhabi',
        distance: 0.9,
        isAvailableForSchedule: true,
        certifications: [
          'American Board of Cardiology',
          'European Society of Cardiology',
        ],
        yearsOfExperience: 10,
        isVerified: true,
      ),
      DoctorModel(
        id: '2',
        name: 'Dr. Kamal Hossain',
        specialty: 'Neurologist',
        imageUrl: 'assets/images/doctor2.png',
        rating: 4.6,
        reviewCount: 98,
        about:
            'Dr. Kamal Hossain is a neurologist specializing in the diagnosis and treatment of disorders of the nervous system.',
        phoneNumber: '+8801798765432',
        email: '<EMAIL>',
        location: DoctorLocationModel(
          id: '2',
          latitude: latitude - 0.01,
          longitude: longitude + 0.02,
          address: '456 Brain Center',
          city: 'Dhaka',
          state: 'Dhaka',
          country: 'Bangladesh',
          zipCode: '1205',
          name: 'Brain Center',
          distance: 1.2,
        ),
        availability: _getMockAvailability(),
        consultationFee: 2000,
        services: [
          'Neurological Consultation',
          'EEG',
          'EMG',
          'Nerve Conduction Study',
        ],
        languages: ['Bengali', 'English', 'Hindi'],
        education: 'MBBS, MD (Neurology), Fellowship in Stroke Medicine',
        experience: '8+ years in neurology',
        hospitalName: 'NMC Specialty Hospital Abu Dhabi',
        distance: 1.2,
        isAvailableForSchedule: true,
        certifications: [
          'American Board of Neurology',
          'International Neurological Society',
        ],
        yearsOfExperience: 8,
        isVerified: true,
      ),
      DoctorModel(
        id: '3',
        name: 'Dr. Fatima Begum',
        specialty: 'Gynecologist',
        imageUrl: 'assets/images/doctor3.png',
        rating: 4.9,
        reviewCount: 156,
        about:
            'Dr. Fatima Begum is a gynecologist with expertise in women\'s reproductive health.',
        phoneNumber: '+8801612345678',
        email: '<EMAIL>',
        location: DoctorLocationModel(
          id: '3',
          latitude: latitude + 0.02,
          longitude: longitude + 0.01,
          address: '789 Women\'s Health Clinic',
          city: 'Dhaka',
          state: 'Dhaka',
          country: 'Bangladesh',
          zipCode: '1212',
          name: 'Women\'s Health Clinic',
          distance: 1.5,
        ),
        availability: _getMockAvailability(),
        consultationFee: 1800,
        services: [
          'Gynecological Consultation',
          'Prenatal Care',
          'Pap Smear',
          'Ultrasound',
        ],
        languages: ['Bengali', 'English'],
        education: 'MBBS, FCPS (Gynecology & Obstetrics)',
        experience: '12+ years in gynecology',
        hospitalName: 'NMC Specialty Hospital Abu Dhabi',
        distance: 1.5,
        isAvailableForSchedule: true,
        certifications: [
          'American Board of Obstetrics and Gynecology',
          'Royal College of Obstetricians and Gynaecologists',
        ],
        yearsOfExperience: 12,
        isVerified: true,
      ),
      DoctorModel(
        id: '4',
        name: 'Dr. John Smith',
        specialty: 'Family Doctor',
        imageUrl: 'assets/images/doctor4.png',
        rating: 4.7,
        reviewCount: 112,
        about:
            'Dr. John Smith is a family physician with a focus on preventive care and holistic health management.',
        phoneNumber: '+8801812345678',
        email: '<EMAIL>',
        location: DoctorLocationModel(
          id: '4',
          latitude: latitude - 0.02,
          longitude: longitude - 0.02,
          address: '321 Family Health Center',
          city: 'Dhaka',
          state: 'Dhaka',
          country: 'Bangladesh',
          zipCode: '1230',
          name: 'Family Health Center',
          distance: 2.1,
        ),
        availability: _getMockAvailability(),
        consultationFee: 1200,
        services: [
          'General Check-up',
          'Vaccination',
          'Health Screening',
          'Chronic Disease Management',
        ],
        languages: ['English', 'French'],
        education: 'MD, Family Medicine, University of Toronto',
        experience: '15+ years in family medicine',
        hospitalName: 'NMC Specialty Hospital Abu Dhabi',
        distance: 2.1,
        isAvailableForSchedule: false,
        certifications: [
          'American Board of Family Medicine',
          'Canadian College of Family Physicians',
        ],
        yearsOfExperience: 15,
        isVerified: true,
      ),
      DoctorModel(
        id: '5',
        name: 'Dr. Sarah Johnson',
        specialty: 'Dentist',
        imageUrl: 'assets/images/doctor5.png',
        rating: 4.5,
        reviewCount: 87,
        about:
            'Dr. Sarah Johnson is a dentist specializing in cosmetic dentistry and oral health.',
        phoneNumber: '+8801912345678',
        email: '<EMAIL>',
        location: DoctorLocationModel(
          id: '5',
          latitude: latitude + 0.03,
          longitude: longitude - 0.03,
          address: '555 Dental Care Center',
          city: 'Dhaka',
          state: 'Dhaka',
          country: 'Bangladesh',
          zipCode: '1240',
          name: 'Dental Care Center',
          distance: 2.8,
        ),
        availability: _getMockAvailability(),
        consultationFee: 1300,
        services: [
          'Dental Check-up',
          'Teeth Cleaning',
          'Cosmetic Dentistry',
          'Root Canal Treatment',
        ],
        languages: ['English'],
        education: 'DDS, University of California',
        experience: '7+ years in dentistry',
        hospitalName: 'Dental Care Clinic',
        distance: 2.8,
        isAvailableForSchedule: true,
        certifications: [
          'American Dental Association',
          'Academy of General Dentistry',
        ],
        yearsOfExperience: 7,
        isVerified: false,
      ),
    ];
  }

  DoctorAvailabilityModel _getMockAvailability() {
    return DoctorAvailabilityModel(
      schedule: [
        DayScheduleModel(
          day: 'Monday',
          isAvailable: true,
          timeSlots: [
            TimeSlotModel(startTime: '09:00', endTime: '12:00'),
            TimeSlotModel(startTime: '14:00', endTime: '17:00'),
          ],
        ),
        DayScheduleModel(
          day: 'Tuesday',
          isAvailable: true,
          timeSlots: [
            TimeSlotModel(startTime: '09:00', endTime: '12:00'),
            TimeSlotModel(startTime: '14:00', endTime: '17:00'),
          ],
        ),
        DayScheduleModel(
          day: 'Wednesday',
          isAvailable: true,
          timeSlots: [
            TimeSlotModel(startTime: '09:00', endTime: '12:00'),
            TimeSlotModel(startTime: '14:00', endTime: '17:00'),
          ],
        ),
        DayScheduleModel(
          day: 'Thursday',
          isAvailable: true,
          timeSlots: [
            TimeSlotModel(startTime: '09:00', endTime: '12:00'),
            TimeSlotModel(startTime: '14:00', endTime: '17:00'),
          ],
        ),
        DayScheduleModel(day: 'Friday', isAvailable: false, timeSlots: []),
        DayScheduleModel(
          day: 'Saturday',
          isAvailable: true,
          timeSlots: [TimeSlotModel(startTime: '10:00', endTime: '14:00')],
        ),
        DayScheduleModel(day: 'Sunday', isAvailable: false, timeSlots: []),
      ],
    );
  }
}
