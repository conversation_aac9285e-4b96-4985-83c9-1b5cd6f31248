import 'package:flutter/material.dart';

enum RadiusType { suggested, custom }

class SearchRadiusSelector extends StatefulWidget {
  final double radius;
  final Function(double) onRadiusChanged;
  final RadiusType radiusType;
  final Function(RadiusType) onRadiusTypeChanged;
  final VoidCallback onEdit;
  final bool isCompact;

  const SearchRadiusSelector({
    super.key,
    required this.radius,
    required this.onRadiusChanged,
    required this.radiusType,
    required this.onRadiusTypeChanged,
    required this.onEdit,
    this.isCompact = false,
  });

  @override
  State<SearchRadiusSelector> createState() => _SearchRadiusSelectorState();
}

class _SearchRadiusSelectorState extends State<SearchRadiusSelector> {
  late double _currentRadius;
  late RadiusType _currentRadiusType;

  @override
  void initState() {
    super.initState();
    _currentRadius = widget.radius;
    _currentRadiusType = widget.radiusType;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCompact) {
      return compactView(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
          child: Text(
            'Selected search radius',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),
        _buildRadiusTypeOption(
          title: 'Suggested radius',
          type: RadiusType.suggested,
        ),
        _buildRadiusTypeOption(title: 'Custom radius', type: RadiusType.custom),
        if (_currentRadiusType == RadiusType.custom) _buildRadiusSlider(),
      ],
    );
  }

  Widget _buildRadiusTypeOption({
    required String title,
    required RadiusType type,
  }) {
    final isSelected = _currentRadiusType == type;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: const Color(0xFF2563EB), width: 2),
              color: isSelected ? const Color(0xFF2563EB) : Colors.white,
            ),
            child:
                isSelected
                    ? const Icon(Icons.check, size: 16, color: Colors.white)
                    : null,
          ),
        ],
      ),
    );
  }

  Widget _buildRadiusSlider() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_currentRadius.toStringAsFixed(1)} KM',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Slider(
          value: _currentRadius,
          min: 1.0,
          max: 10.0,
          divisions: 18,
          activeColor: const Color(0xFF2563EB),
          inactiveColor: Colors.grey[300],
          label: '${_currentRadius.toStringAsFixed(1)} KM',
          onChanged: (value) {
            setState(() {
              _currentRadius = value;
            });
            widget.onRadiusChanged(value);
          },
        ),
      ],
    );
  }

  Widget compactView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Search radius',
            style: TextStyle(fontSize: 14, color: Colors.black54),
          ),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_currentRadius.toStringAsFixed(1)} KM radius',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextButton(
                onPressed: widget.onEdit,
                child: const Text(
                  'Edit',
                  style: TextStyle(
                    color: Color(0xFF2563EB),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
