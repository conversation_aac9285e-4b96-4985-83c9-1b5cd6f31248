import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';

class DoctorSearchFilterSection extends StatelessWidget {
  final String? selectedDoctorType;
  final double searchRadius;
  final VoidCallback onDoctorTypeSelect;
  final VoidCallback onRadiusSelect;
  final VoidCallback onSearch;

  const DoctorSearchFilterSection({
    super.key,
    required this.selectedDoctorType,
    required this.searchRadius,
    required this.onDoctorTypeSelect,
    required this.onRadiusSelect,
    required this.onSearch,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Doctor type selector
        _buildSelectorRow(
          label: 'Doctor type',
          value: selectedDoctorType ?? 'Unspecified',
          onSelect: onDoctorTypeSelect,
        ),

        SizedBox(height: 12), // Area radius selector
        _buildSelectorRow(
          label: 'Area radius',
          value: '${searchRadius.toInt()} meters',
          onSelect: onRadiusSelect,
        ),

        const Gap(12),

        // Search button
        CustomElevatedButton(onPressed: onSearch, text: 'Search'),
      ],
    );
  }

  Widget _buildSelectorRow({
    required String label,
    required String value,
    required VoidCallback onSelect,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
            Text(value, style: const TextStyle(fontSize: 16)),
          ],
        ),
        TextButton(
          onPressed: onSelect,
          child: Text(
            'Select',
            style: const TextStyle(fontSize: 14, color: AppConstants.primaryColor),
          ),
        ),
      ],
    );
  }
}
