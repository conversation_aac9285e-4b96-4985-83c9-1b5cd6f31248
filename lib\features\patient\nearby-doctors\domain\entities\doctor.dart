import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_location.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';

class Doctor extends Equatable {
  final String id;
  final String name;
  final String specialty;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String about;
  final String phoneNumber;
  final String email;
  final DoctorLocation location;
  final DoctorAvailability availability;
  final double consultationFee;
  final List<String> services;
  final List<String> languages;
  final String education;
  final String experience;
  final String hospitalName;
  final double distance;
  final bool isAvailableForSchedule;
  final List<String> certifications;
  final int yearsOfExperience;
  final bool isVerified;

  const Doctor({
    required this.id,
    required this.name,
    required this.specialty,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.about,
    required this.phoneNumber,
    required this.email,
    required this.location,
    required this.availability,
    required this.consultationFee,
    required this.services,
    required this.languages,
    required this.education,
    required this.experience,
    this.hospitalName = '',
    this.distance = 0.0,
    this.isAvailableForSchedule = false,
    this.certifications = const [],
    this.yearsOfExperience = 0,
    this.isVerified = false,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    specialty,
    imageUrl,
    rating,
    reviewCount,
    about,
    phoneNumber,
    email,
    location,
    availability,
    consultationFee,
    services,
    languages,
    education,
    experience,
    hospitalName,
    distance,
    isAvailableForSchedule,
    certifications,
    yearsOfExperience,
    isVerified,
  ];
}
