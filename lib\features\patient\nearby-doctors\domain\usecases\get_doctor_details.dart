import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class GetDoctorDetails implements UseCase<Doctor, DoctorDetailsParams> {
  final DoctorRepository repository;

  GetDoctorDetails(this.repository);

  @override
  Future<Either<Failure, Doctor>> call(DoctorDetailsParams params) async {
    try {
      final result = await repository.getDoctorDetails(params.doctorId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}

class DoctorDetailsParams extends Equatable {
  final String doctorId;

  const DoctorDetailsParams({required this.doctorId});

  @override
  List<Object?> get props => [doctorId];
}
