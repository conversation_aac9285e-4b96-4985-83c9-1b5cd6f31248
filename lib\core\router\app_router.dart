import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/di/injection_container.dart';
import 'package:imed_fe/features/patient/appointments/presentation/pages/doctor_appointment_booking_page.dart';
import 'package:imed_fe/features/patient/appointments/presentation/pages/doctor_appointment_history_page.dart';
import 'package:imed_fe/features/patient/appointments/presentation/pages/doctor_appointment_notifications_page.dart';
import 'package:imed_fe/features/patient/appointments/presentation/pages/doctor_appointment_submit_page.dart';
import 'package:imed_fe/features/patient/appointments/presentation/pages/doctor_appointment_with_booking_id_page.dart';
import 'package:imed_fe/features/auth/presentation/pages/set_password_page.dart';
import 'package:imed_fe/features/auth/presentation/pages/signin_page.dart';
import 'package:imed_fe/features/auth/presentation/pages/signup_page.dart';
import 'package:imed_fe/features/auth/presentation/pages/splash_page.dart';
import 'package:imed_fe/features/patient/consultation/presentation/pages/doctor_connection_page.dart';
import 'package:imed_fe/features/patient/consultation/presentation/pages/doctor_patient_calling_page.dart';
import 'package:imed_fe/features/patient/consultation/presentation/pages/prescription_view_page.dart';
import 'package:imed_fe/features/patient/consultation/presentation/pages/uploading_prescription_page.dart';
import 'package:imed_fe/features/patient/doctor-profile-view/presentation/pages/doctor_info_rate_page.dart';

import 'package:imed_fe/features/patient/doctor-profile-view/presentation/pages/doctor_profile_page.dart';

import 'package:imed_fe/features/patient/doctor-profile-view/presentation/pages/doctor_schedule.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/bloc/doctor_bloc.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/pages/nearby_doctor_search_list.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/pages/nearby_doctor_search_page.dart';
import 'package:imed_fe/features/patient/payment/presentation/pages/doctor_insurance_add_insurance.dart';
import 'package:imed_fe/features/patient/payment/presentation/pages/payment_option.dart';
import 'package:imed_fe/features/patient/payment/presentation/pages/payment_success.dart';
import 'package:imed_fe/features/patient/payment/presentation/pages/register_screen.dart';
import 'package:imed_fe/features/patient/profile/presentation/pages/patient_profile_page.dart';
import 'package:imed_fe/features/patient/report/presentation/pages/doctor_report_prescription_and_diagnostics_report_page.dart';
import 'package:imed_fe/features/patient/report/presentation/pages/doctor_report_upload_prescription_ocr.dart';
import 'package:imed_fe/features/patient/report/presentation/pages/doctor_report_upload_success.dart';
import 'package:imed_fe/features/patient/report/presentation/pages/doctor_report_uploaded_report_captured.dart';

class AppRouter {
  // Define the routes as static constants
  static const String initial = '/';
  static const String signin = '/signin';
  static const String signup = '/signup';
  static const String setPassword = '/set-password';

  static const String onboarding = '/onboarding';

  static const String patientProfile = '/patient-profile';

  static const String nearbyDoctorsSearch = '/nearby-doctors-search';
  static const String doctorsList = '/doctors-list';

  static const String doctorProfileView = '/doctor-profile-view';
  static const String doctorSchedule = '/doctor-schedule';
  static const String doctorInfoRate = '/doctor-info-rate-page';

  static const String register = '/register';
  static const String appointmentSet = '/appointment-set';
  static const String appointmentSetwithBookingID =
      '/appointment-set-with-booking-id';
  static const String history = '/history';
  static const String settingUpAppointment = '/setting-up-appointment';
  static const String paymentSuccess = '/payment-success';
  static const String payment = '/payment';

  static const String notification = '/notification';
  static const String reportUploadSuccess = '/report-upload-success';
  static const String prescriptionAndDiagnosticReport =
      '/prescription-and-diagnostic-report';
  static const String uploadReport = '/upload-report';
  static const String reportsCaptured = '/reports-captured';
  static const String addInsurance = '/add-insurance';
  static const String doctorScheduleView = '/doctor_schedule_view';
  static const String doctorConnection = '/doctor_connection';
  static const String uploadingPrescription = '/uploading_prescription';
  static const String doctorPatientCalling = '/doctor_patient_calling';
  static const String prescriptionView = '/prescription_view';

  static final GoRouter router = GoRouter(
    initialLocation: initial,
    routes: [
      GoRoute(
        path: initial,
        builder: (BuildContext context, GoRouterState state) {
          return const DoctorConnectionPage();
        },
      ),
      GoRoute(
        path: signin,
        builder: (BuildContext context, GoRouterState state) {
          return const SignInPage();
        },
      ),
      GoRoute(
        path: signup,
        builder: (BuildContext context, GoRouterState state) {
          return const SignUpPage();
        },
      ),
      GoRoute(
        path: setPassword,
        builder: (BuildContext context, GoRouterState state) {
          final email = state.extra as String;
          return SetPasswordPage(email: email);
        },
      ),
      GoRoute(
        path: patientProfile,
        builder: (BuildContext context, GoRouterState state) {
          return const PatientProfilePage();
        },
      ),
      GoRoute(
        path: nearbyDoctorsSearch,
        builder: (BuildContext context, GoRouterState state) {
          return BlocProvider<DoctorBloc>(
            create: (context) => sl<DoctorBloc>(),
            child: const NearbyDoctorSearchPage(),
          );
        },
      ),
      GoRoute(
        path: doctorsList,
        builder: (BuildContext context, GoRouterState state) {
          return BlocProvider<DoctorBloc>(
            create: (context) => sl<DoctorBloc>(),
            child: const NearbyDoctorSearchListPage(),
          );
        },
      ),

      GoRoute(
        path: doctorProfileView,
        builder: (BuildContext context, GoRouterState state) {
          return BlocProvider<DoctorBloc>(
            create: (context) => sl<DoctorBloc>(),
            child: const DoctorProfilePage(),
          );
        },
      ),
      GoRoute(
        path: doctorSchedule,
        builder: (BuildContext context, GoRouterState state) {
          return const DoctorSchedulePage();
        },
      ),
      GoRoute(
        path: doctorInfoRate,
        builder: (BuildContext context, GoRouterState state) {
          return const DoctorInfoRatePage();
        },
      ),
      GoRoute(
        path: appointmentSet,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorAppointmentBookingPage(),
      ),
      GoRoute(
        path: appointmentSetwithBookingID,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorAppointmentWithBookingIdPage(),
      ),
      GoRoute(
        path: history,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorAppointmentHistoryPage(),
      ),
      GoRoute(
        path: settingUpAppointment,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorAppointmentSubmitPage(),
      ),
      GoRoute(
        path: paymentSuccess,
        builder:
            (BuildContext context, GoRouterState state) =>
                const PaymentSuccess(),
      ),
      GoRoute(
        path: payment,
        builder:
            (BuildContext context, GoRouterState state) =>
                const PaymentOption(),
      ),
      GoRoute(
        path: register,
        builder:
            (BuildContext context, GoRouterState state) =>
                const RegisterScreen(),
      ),
      GoRoute(
        path: notification,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorAppointmentNotifications(),
      ),
      GoRoute(
        path: reportUploadSuccess,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorReportUploadSuccess(),
      ),
      GoRoute(
        path: prescriptionAndDiagnosticReport,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorReportPrescriptionAndDiagnosticsReportPage(),
      ),
      GoRoute(
        path: uploadReport,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorReportUploadPrescriptionOCR(),
      ),
      GoRoute(
        path: reportsCaptured,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorReportUploadedReportCaptured(),
      ),
      GoRoute(
        path: addInsurance,
        builder:
            (BuildContext context, GoRouterState state) =>
                const DoctorInsuranceAddInsurance(),
      ),
      GoRoute(
        path: doctorConnection,
        builder: (BuildContext context, GoRouterState state) {
          return const DoctorConnectionPage();
        },
      ),
      GoRoute(
        path: uploadingPrescription,
        builder: (BuildContext context, GoRouterState state) {
          return const UploadingPrescriptionPage();
        },
      ),
      GoRoute(
        path: doctorPatientCalling,
        builder: (BuildContext context, GoRouterState state) {
          return const DoctorPatientCallingPage();
        },
      ),
      GoRoute(
        path: prescriptionView,
        builder: (BuildContext context, GoRouterState state) {
          return const PrescriptionViewPage();
        },
      ),
    ],
  );
}
