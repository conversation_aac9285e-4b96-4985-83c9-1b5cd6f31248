import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class CustomAttachmentBox extends StatelessWidget {
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final BorderRadius? borderRadius;

  const CustomAttachmentBox({
    super.key,
    this.onTap,
    this.onChanged,
    this.backgroundColor = AppConstants.inputFieldBackgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius:
              borderRadius ??
              BorderRadius.circular(AppConstants.borderRadiusSmall),
        ),
        padding: EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ReusableSvgImage(
              assetPath: "assets/icons/patient/clip_icon.svg",
              height: 16.53,
            ),
            const SizedBox(width: 16),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Add attachments",
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppConstants.textHighColor,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    "Previous prescription, reports, anything helps with your consultation",
                    softWrap: true,
                    overflow: TextOverflow.visible,
                    maxLines: 2,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF718DE0),
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    "File format : PNG or JPEG Max 2 MB",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF718DE0),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
