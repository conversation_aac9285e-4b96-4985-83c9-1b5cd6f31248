import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import '../../../../../core/constants/app_constants.dart';
import '../widgets/keep_in_mind_section.dart';

class UploadingPrescriptionPage extends StatelessWidget {
  const UploadingPrescriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => context.push(AppRouter.prescriptionView),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall + 4,
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: AppConstants.paddingExtraLarge - 8,
                      backgroundImage: AssetImage(
                        'assets/images/doctor_pic.png',
                      ),
                    ),
                    SizedBox(width: AppConstants.paddingSmall + 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Dr. Rhonda Rhodes",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textPrimaryColor,
                          ),
                        ),
                        SizedBox(height: AppConstants.sizedBoxHeightSmall - 4),
                        Text(
                          "Uploading your prescription",
                          style: TextStyle(
                            color: AppConstants.textMidColor,
                            fontSize: AppConstants.fontSizeSmall + 1,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Spacer(),
              Column(
                children: [
                  Icon(
                    Icons.autorenew,
                    color: AppConstants.primaryColor,
                    size: 80,
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightMedium + 4),
                  Text(
                    "Please Wait",
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeExtraLarge - 4,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Text(
                    "This may take up to 5 minutes",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppConstants.textMidColor,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall - 4),
                  Text(
                    "The doctor is writing your prescription",
                    style: TextStyle(color: AppConstants.textMidColor),
                  ),
                ],
              ),
              Spacer(),
              KeepInMIndSection(),
            ],
          ),
        ),
      ),
    );
  }
}
