import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';

class PatientRegistrationResponseModel extends PatientRegistrationResponse {
  const PatientRegistrationResponseModel({
    required super.id,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory PatientRegistrationResponseModel.fromJson(Map<String, dynamic> json) {
    return PatientRegistrationResponseModel(
      id: json['data']['id'] as String,
      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'id': id,
      },
      'message': message,
      'status': status,
      'statusCode': statusCode,
    };
  }
}
