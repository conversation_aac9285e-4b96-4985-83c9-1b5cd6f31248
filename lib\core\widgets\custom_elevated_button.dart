import 'package:flutter/material.dart';

class CustomElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;

  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? borderRadius;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Widget? prefixIcon; // Add this line

  const CustomElevatedButton({
    super.key,
    this.onPressed,
    required this.text,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.fontSize,
    this.fontWeight,
    this.prefixIcon, // Add this line
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        shape:
            borderRadius != null
                ? RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius!),
                )
                : null,
      ),
      child:
          prefixIcon != null
              ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  prefixIcon!,
                  const SizedBox(width: 8),
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: fontWeight,
                    ),
                  ),
                ],
              )
              : Text(
                text,
                style: TextStyle(fontSize: fontSize, fontWeight: fontWeight),
              ),
    );
  }
}
