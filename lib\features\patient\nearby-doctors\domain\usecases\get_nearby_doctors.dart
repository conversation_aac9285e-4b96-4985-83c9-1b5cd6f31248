import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class GetNearbyDoctors implements UseCase<List<Doctor>, NearbyDoctorsParams> {
  final DoctorRepository repository;

  GetNearbyDoctors(this.repository);

  @override
  Future<Either<Failure, List<Doctor>>> call(NearbyDoctorsParams params) async {
    try {
      final result = await repository.getNearbyDoctors(
        latitude: params.latitude,
        longitude: params.longitude,
        radius: params.radius,
        specialty: params.specialty,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}

class NearbyDoctorsParams extends Equatable {
  final double latitude;
  final double longitude;
  final double radius;
  final String? specialty;

  const NearbyDoctorsParams({
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.specialty,
  });

  @override
  List<Object?> get props => [latitude, longitude, radius, specialty];
}
