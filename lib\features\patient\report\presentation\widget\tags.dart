import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class Tags extends StatelessWidget {
  final String tagName;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  const Tags({
    super.key,
    required this.tagName,
    this.backgroundColor = const Color(0xFFEDF2FF),
    this.foregroundColor = AppConstants.textHighColor,
    this.fontSize = 14.0,
    this.padding = const EdgeInsets.symmetric(
      horizontal: AppConstants.paddingMedium,
      vertical: AppConstants.paddingSmall,
    ),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        tagName,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: fontSize,
          color: foregroundColor,
        ),
      ),
    );
  }
}
