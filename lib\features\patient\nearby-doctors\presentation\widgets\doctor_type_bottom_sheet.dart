import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_type.dart';

class DoctorTypeBottomSheet extends StatelessWidget {
  final String? selectedDoctorType;
  final Function(String) onTypeSelected;
  final List<DoctorType> doctorTypes;

  const DoctorTypeBottomSheet({
    Key? key,
    required this.selectedDoctorType,
    required this.onTypeSelected,
    required this.doctorTypes,
  }) : super(key: key);

  static void show({
    required BuildContext context,
    required String? selectedDoctorType,
    required Function(String) onTypeSelected,
  }) {
    final types = [
      DoctorType(
        name: 'Family Doctor',
        assetPath: 'assets/icons/patient/family.svg',
        doctorCount: 8,
      ),
      DoctorType(
        name: 'Medicine',
        assetPath: 'assets/icons/patient/medicine.svg',
        doctorCount: 8,
      ),
      DoctorType(
        name: 'Dentist',
        assetPath: 'assets/icons/patient/dentist.svg',
        doctorCount: 21,
      ),
      DoctorType(
        name: 'Pulmologist',
        assetPath: 'assets/icons/patient/heart.svg',
        doctorCount: 19,
      ),
      DoctorType(
        name: 'Gastrologist',
        assetPath: 'assets/icons/patient/stomach.svg',

        doctorCount: 8,
      ),
      DoctorType(
        name: 'Cardiologist',
        assetPath: 'assets/icons/patient/lungs.svg',
        doctorCount: 8,
      ),
      DoctorType(
        name: 'Optomologist',
        assetPath: 'assets/icons/patient/heart.svg',
        doctorCount: 8,
      ),
    ];

    showModalBottomSheet(
      context: context,
      builder:
          (context) => DoctorTypeBottomSheet(
            selectedDoctorType: selectedDoctorType,
            onTypeSelected: onTypeSelected,
            doctorTypes: types,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.borderRadiusMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Types', style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: AppConstants.borderRadiusLarge),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: doctorTypes.length,
              itemBuilder: (context, index) {
                final type = doctorTypes[index];
                return InkWell(
                  onTap: () {
                    onTypeSelected(type.name);
                    Navigator.pop(context);
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(
                      vertical: AppConstants.borderRadiusSmall,
                    ),
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: AppConstants.inputFieldBackgroundColor2,
                      border: Border.all(
                        color:
                            selectedDoctorType == type.name
                                ? AppConstants.primaryColor
                                : Colors.transparent,
                        width: 2,
                      ),

                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusLarge,
                      ),
                    ),
                    child: Row(
                      children: [
                        ReusableSvgImage(assetPath: type.assetPath),
                        const SizedBox(width: AppConstants.paddingSmall),
                        Text(
                          type.name,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Spacer(),
                        Text(
                          '${type.doctorCount} Doctor',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          CustomOutlinedButton(
            text: "View all",
            borderRadius: AppConstants.borderRadiusMedium,
            borderColor: AppConstants.primaryColor,
            width: double.infinity,
            onPressed: () => context.pop(),
          ),
        ],
      ),
    );
  }
}
