import 'package:flutter/material.dart';

class ProfilePic extends StatelessWidget {
  final String imageUrl;
  final double borderRadius;
  final double width;
  final double height;
  const ProfilePic({
    super.key,
    required this.imageUrl,
    this.borderRadius = 3.2,
    this.width = 80,
    this.height = 80,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: Image.asset(
        imageUrl,
        fit: BoxFit.cover,
        width: width,
        height: height,
      ),
    );
  }
}
