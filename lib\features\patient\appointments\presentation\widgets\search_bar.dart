import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class CustomSearchBar extends StatelessWidget {
  final String hintText;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final BorderRadius? borderRadius;

  const CustomSearchBar({
    super.key,
    this.hintText = 'Search',
    this.onTap,
    this.onChanged,
    this.backgroundColor = AppConstants.inputFieldBackgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius:
              borderRadius ??
              BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            const Icon(Icons.search, color: Color(0xFF6C6E74), size: 15),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child:
                  onChanged != null
                      ? TextField(
                        decoration: InputDecoration(
                          hintText: hintText,
                          hintStyle: const TextStyle(
                            color: AppConstants.inputFieldForegroundColor,
                            fontSize: AppConstants.fontSizeMedium,
                          ),
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                        onChanged: onChanged,
                      )
                      : Text(
                        hintText,
                        style: TextStyle(
                          color: AppConstants.inputFieldForegroundColor,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
