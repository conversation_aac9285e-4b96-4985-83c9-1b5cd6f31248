import 'package:imed_fe/features/patient/profile/data/datasources/home_remote_data_source.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/user_profile.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/health_score.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/bed_time.dart';
import 'package:imed_fe/features/patient/profile/domain/repositories/home_repository.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;

  HomeRepositoryImpl({required this.remoteDataSource});

  @override
  Future<UserProfile> getUserProfile() async {
    return await remoteDataSource.getUserProfile();
  }

  @override
  Future<HealthScore> getHealthScore() async {
    return await remoteDataSource.getHealthScore();
  }

  @override
  Future<BedTime> getBedTime() async {
    return await remoteDataSource.getBedTime();
  }
}
