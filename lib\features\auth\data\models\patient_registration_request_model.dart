import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';

class PatientRegistrationRequestModel extends PatientRegistrationRequest {
  const PatientRegistrationRequestModel({
    required super.roleId,
    required super.firstName,
    required super.lastName,
    required super.phone,
    required super.dateOfBirth,
    required super.gender,
  });

  factory PatientRegistrationRequestModel.fromEntity(
    PatientRegistrationRequest entity,
  ) {
    return PatientRegistrationRequestModel(
      roleId: entity.roleId,
      firstName: entity.firstName,
      lastName: entity.lastName,
      phone: entity.phone,
      dateOfBirth: entity.dateOfBirth,
      gender: entity.gender,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'role_id': roleId,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'date_of_birth': dateOfBirth,
      'gender': gender,
    };
  }
}
