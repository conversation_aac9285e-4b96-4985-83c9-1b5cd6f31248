import 'package:flutter/material.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class QuickAccessCard extends StatelessWidget {
  final String iconAsset;
  final String shadowIconAsset;
  final String title;
  final Color color;

  final VoidCallback? onTap;

  const QuickAccessCard({
    super.key,
    required this.iconAsset,
    required this.title,
    required this.color,
    this.onTap,
    required this.shadowIconAsset,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),

        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ReusableSvgImage(assetPath: iconAsset),

                ReusableSvgImage(assetPath: shadowIconAsset),
              ],
            ),
            Text(
              title,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 25),
            ),
          ],
        ),
      ),
    );
  }
}
