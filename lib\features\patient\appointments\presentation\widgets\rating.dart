import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class Rating extends StatelessWidget {
  final double? rating;
  final int? reviewCount;
  final Color? iconColor;
  final String? countAbout;
  const Rating({
    super.key,
    this.rating,
    this.reviewCount,
    this.iconColor,
    this.countAbout = "reviews",
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ReusableSvgImage(
          assetPath: 'assets/icons/common/star_icon.svg',
          color: iconColor,
        ),
        const SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Text(
          "${rating.toString()}/5",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Text(
          '(${reviewCount.toString()} $countAbout)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
