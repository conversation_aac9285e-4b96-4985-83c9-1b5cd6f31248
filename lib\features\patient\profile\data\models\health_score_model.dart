import 'package:imed_fe/features/patient/profile/domain/entities/health_score.dart';

class HealthScoreModel extends HealthScore {
  const HealthScoreModel({required int score, required String status})
    : super(score: score, status: status);

  factory HealthScoreModel.fromJson(Map<String, dynamic> json) {
    return HealthScoreModel(
      score: json['score'] ?? 0,
      status: json['status'] ?? 'Unknown',
    );
  }

  Map<String, dynamic> toJson() {
    return {'score': score, 'status': status};
  }
}
