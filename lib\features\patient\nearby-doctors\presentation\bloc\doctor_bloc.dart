import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/filter_doctors.dart'
    as filter_doctors;
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_availability.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_details.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_doctor_specialties.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/usecases/get_nearby_doctors.dart';

part 'doctor_event.dart';
part 'doctor_state.dart';

class DoctorB<PERSON> extends Bloc<DoctorEvent, DoctorState> {
  final GetNearbyDoctors getNearbyDoctors;
  final GetDoctorDetails getDoctorDetails;
  final GetDoctorAvailability getDoctorAvailability;
  final filter_doctors.FilterDoctors filterDoctorsUseCase;
  final GetDoctorSpecialties getDoctorSpecialties;

  DoctorBloc({
    required this.getNearbyDoctors,
    required this.getDoctorDetails,
    required this.getDoctorAvailability,
    required this.filterDoctorsUseCase,
    required this.getDoctorSpecialties,
  }) : super(DoctorInitial()) {
    on<LoadNearbyDoctors>(_onLoadNearbyDoctors);
    on<LoadDoctorDetails>(_onLoadDoctorDetails);
    on<LoadDoctorAvailability>(_onLoadDoctorAvailability);
    on<FilterDoctors>(_onFilterDoctors);
    on<LoadDoctorSpecialties>(_onLoadDoctorSpecialties);
  }

  Future<void> _onLoadNearbyDoctors(
    LoadNearbyDoctors event,
    Emitter<DoctorState> emit,
  ) async {
    emit(DoctorLoading());
    final result = await getNearbyDoctors(
      NearbyDoctorsParams(
        latitude: event.latitude,
        longitude: event.longitude,
        radius: event.radius,
        specialty: event.specialty,
      ),
    );

    result.fold(
      (failure) => emit(DoctorError(message: failure.message)),
      (doctors) => emit(NearbyDoctorsLoaded(doctors: doctors)),
    );
  }

  Future<void> _onLoadDoctorDetails(
    LoadDoctorDetails event,
    Emitter<DoctorState> emit,
  ) async {
    emit(DoctorLoading());
    final result = await getDoctorDetails(
      DoctorDetailsParams(doctorId: event.doctorId),
    );

    result.fold(
      (failure) => emit(DoctorError(message: failure.message)),
      (doctor) => emit(DoctorDetailsLoaded(doctor: doctor)),
    );
  }

  Future<void> _onLoadDoctorAvailability(
    LoadDoctorAvailability event,
    Emitter<DoctorState> emit,
  ) async {
    emit(DoctorLoading());
    final result = await getDoctorAvailability(
      DoctorAvailabilityParams(doctorId: event.doctorId),
    );

    result.fold(
      (failure) => emit(DoctorError(message: failure.message)),
      (availability) =>
          emit(DoctorAvailabilityLoaded(availability: availability)),
    );
  }

  Future<void> _onFilterDoctors(
    FilterDoctors event,
    Emitter<DoctorState> emit,
  ) async {
    emit(DoctorLoading());
    final result = await filterDoctorsUseCase(
      filter_doctors.FilterDoctorsParams(
        latitude: event.latitude,
        longitude: event.longitude,
        radius: event.radius,
        specialty: event.specialty,
        minRating: event.minRating,
        isAvailableForSchedule: event.isAvailableForSchedule,
        searchQuery: event.searchQuery,
      ),
    );

    result.fold(
      (failure) => emit(DoctorError(message: failure.message)),
      (doctors) => emit(
        FilteredDoctorsLoaded(
          doctors: doctors,
          specialty: event.specialty,
          minRating: event.minRating,
          isAvailableForSchedule: event.isAvailableForSchedule,
          searchQuery: event.searchQuery,
        ),
      ),
    );
  }

  Future<void> _onLoadDoctorSpecialties(
    LoadDoctorSpecialties event,
    Emitter<DoctorState> emit,
  ) async {
    emit(DoctorLoading());
    final result = await getDoctorSpecialties(NoParams());

    result.fold(
      (failure) => emit(DoctorError(message: failure.message)),
      (specialties) => emit(DoctorSpecialtiesLoaded(specialties: specialties)),
    );
  }
}
