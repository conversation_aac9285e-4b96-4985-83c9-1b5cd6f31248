import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/entities/registration_data.dart';
import 'package:imed_fe/features/auth/domain/entities/user.dart';
import 'package:imed_fe/features/auth/domain/repositories/auth_repository.dart';

class SignUp implements UseCase<User, RegistrationData> {
  final AuthRepository repository;

  SignUp(this.repository);

  @override
  Future<Either<Failure, User>> call(RegistrationData params) async {
    return await repository.signUp(params);
  }
}