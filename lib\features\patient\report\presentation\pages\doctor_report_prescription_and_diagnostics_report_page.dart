import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/search_bar.dart';

class DoctorReportPrescriptionAndDiagnosticsReportPage extends StatelessWidget {
  const DoctorReportPrescriptionAndDiagnosticsReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    List reports = [
      {
        'name': 'Dr <PERSON><PERSON><PERSON>',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/person1.png',
      },
      {
        'name': 'Dr <PERSON><PERSON><PERSON>',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/person2.png',
      },
      {
        'name': 'CBC, RBS, ESR, SGPT (ALT)',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/company1.png',
      },
      {
        'name': 'Dr Rhonda Rhodes',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/person3.png',
      },
      {
        'name': 'Urine R/E, S.Iron, S.Calcium',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/company2.png',
      },
      {
        'name': 'Img 1827390',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/person_default.png',
      },
      {
        'name': 'Img 1827332',
        'timestamp': '20 Apr 2025; 12:20 PM',
        'imageUrl': 'assets/images/person_default.png',
      },
    ];

    return Scaffold(
      appBar: ReusableAppBar(
        title: Text(
          "Prescription & diagnostics reports",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                ),
                child: Column(
                  children: [
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    CustomSearchBar(),
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    _FilterBox(),
                    SizedBox(height: AppConstants.sizedBoxHeightMedium),
                    Row(children: [_UploadReport()]),
                    SizedBox(height: AppConstants.sizedBoxHeightMedium),
                    _ReportsList(list: reports),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FilterBox extends StatelessWidget {
  const _FilterBox();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.paddingSmall,
        ),
        child: GestureDetector(
          child: Row(
            children: [
              CustomOutlinedButton(
                text: 'All',
                foregroundColor: AppConstants.primaryColor,
                borderColor: AppConstants.primaryColor,
              ),
              SizedBox(width: AppConstants.sizedBoxHeightSmall),
              CustomOutlinedButton(
                text: 'Prescriptions',
                foregroundColor: AppConstants.textMidColor,
                borderColor: AppConstants.borderColor,
              ),
              SizedBox(width: AppConstants.sizedBoxHeightSmall),
              CustomOutlinedButton(
                text: 'Diagnostics Reports',
                foregroundColor: AppConstants.textMidColor,
                borderColor: AppConstants.borderColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _UploadReport extends StatelessWidget {
  const _UploadReport();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppConstants.inputFieldBackgroundColor,
        border: Border.all(color: AppConstants.inputFieldForegroundColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: GestureDetector(
          onTap: () {
            context.push(AppRouter.uploadReport);
          },
          child: Column(
            children: [
              ReusableSvgImage(
                assetPath: 'assets/icons/patient/plus_icon.svg',
                width: 32,
                height: 32,
              ),
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              Text(
                'Upload your Report',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ReportsList extends StatelessWidget {
  final List list;
  const _ReportsList({required this.list});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Column(
          spacing: 18,
          children: List.generate(
            list.length,
            (index) => _ReportsListItem(
              name: list[index]['name'],
              timestamp: list[index]['timestamp'],
              imageUrl: list[index]['imageUrl'],
            ),
          ),
        ),
      ],
    );
  }
}

class _ReportsListItem extends StatelessWidget {
  final String name;
  final String timestamp;
  final String imageUrl;
  const _ReportsListItem({
    required this.name,
    required this.timestamp,
    this.imageUrl = '',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ClipOval(
          child: Image.asset(
            imageUrl,
            width: 40,
            height: 40,
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
              SizedBox(height: 2),
              Text(
                timestamp,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textMidColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: AppConstants.sizedBoxHeightSmall),
        ReusableSvgImage(
          assetPath: 'assets/icons/patient/more_icon.svg',
          width: 16,
          height: 16,
        ),
      ],
    );
  }
}
