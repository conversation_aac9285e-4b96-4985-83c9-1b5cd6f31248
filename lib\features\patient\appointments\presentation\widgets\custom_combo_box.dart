import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class CustomComboBox extends StatelessWidget {
  final String? hintText;
  final String? labelText;
  final String? iconPath;
  final int maxLines;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final BorderRadius? borderRadius;

  const CustomComboBox({
    super.key,
    this.labelText = "Label text",
    this.hintText = "Hint text",
    this.iconPath,
    this.maxLines = 1,
    this.onTap,
    this.onChanged,
    this.backgroundColor = AppConstants.inputFieldBackgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null)
          Text(
            labelText!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppConstants.textMidColor,
            ),
          ),
        const Gap(8),
        GestureDetector(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                if (iconPath != null) ...[
                  ReusableSvgImage(
                    assetPath: iconPath!,
                    width: 16.67,
                    height: 16.67,
                  ),
                  const SizedBox(width: 16),
                ] else
                  const SizedBox(),

                Expanded(
                  child: SizedBox(
                    height: 18,
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        hint: Text(
                          hintText ?? '',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color: Color(0xFF718DE0),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        value: null,
                        items: const [],
                        onChanged: (value) {
                          if (onChanged != null && value != null) {
                            onChanged!(value);
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
