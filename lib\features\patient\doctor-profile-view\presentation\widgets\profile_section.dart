import 'package:flutter/material.dart';

class ProfileSection extends StatelessWidget {
  const ProfileSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: <PERSON>umn(
        children: const [
          HospitalExperienceCard(),
          Si<PERSON><PERSON><PERSON>(height: 12),
          HospitalExperienceCard(),
          Si<PERSON><PERSON>ox(height: 12),
          HospitalExperienceCard(),
        ],
      ),
    );
  }
}

class HospitalExperienceCard extends StatelessWidget {
  const HospitalExperienceCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ABC Hospital',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14.5,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: const [
              Expanded(child: InfoColumn(label: 'Designation', value: 'Medical Officer')),
              Expanded(child: InfoColumn(label: 'Department', value: 'Medicine')),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: const [
              Expanded(child: InfoColumn(label: 'Employment Status', value: '1 Jun 2011 - 28 Dec 2016')),
              Expanded(child: InfoColumn(label: 'Period', value: '5 Years')),
            ],
          ),
        ],
      ),
    );
  }
}

class InfoColumn extends StatelessWidget {
  final String label;
  final String value;

  const InfoColumn({super.key, required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 13.5, color: Colors.black54),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 10, color: Colors.black87),
        ),
      ],
    );
  }
}
