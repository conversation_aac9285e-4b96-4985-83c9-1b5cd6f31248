import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/profile_pic.dart';

class PaymentOption extends StatelessWidget {
  const PaymentOption({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        title: Text(
          "Payments",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _PersonsProfile(),
                          SizedBox(height: AppConstants.sizedBoxHeightSmall),
                          _PaymentTips(),
                          SizedBox(height: AppConstants.sizedBoxHeightMedium),
                          _PaymentOption(),
                          SizedBox(height: AppConstants.sizedBoxHeightMedium),
                          _PaymentDetails(),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightMedium),
                  _BottomBar(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PersonsProfile extends StatelessWidget {
  const _PersonsProfile();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _PersonProfileItems(
          role: "Consulting",
          name: "Dr Rhonda Rhodes",
          info: "MBBS FCPS",
          imageUrl: "assets/images/profile_consultant.png",
        ),
        SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Divider(color: Color(0xFFE6E7EB)),
        Column(
          children: [
            _PersonProfileItems(
              role: "Patient information",
              name: "Nasir Hossein",
              info: "MALE, AGE : 46",
              imageUrl: "assets/images/profile_patient.png",
            ),
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Container(
              decoration: BoxDecoration(color: Color(0xFFEDF2FF)),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      "Change patient",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _PersonProfileItems extends StatelessWidget {
  final String role;
  final String name;
  final String info;
  final String imageUrl;
  const _PersonProfileItems({
    required this.role,
    required this.name,
    required this.info,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            role,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Row(
            children: [
              ProfilePic(
                imageUrl: imageUrl,
                width: 48,
                height: 48,
                borderRadius: AppConstants.borderRadiusSmall,
              ),
              SizedBox(width: AppConstants.sizedBoxHeightMedium),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Text(
                    info,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _PaymentTips extends StatelessWidget {
  const _PaymentTips();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Do you have insurance ?",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppConstants.textHighColor,
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Text(
            "Using insurance can reduce your consultation costs. Add your insurance info now or later in your profile.",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppConstants.textMidColor,
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: Color(0xFFEEF5FF),
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusSmall,
                  ),
                ),
                child: Text(
                  "Non Added",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF718DE0),
                  ),
                ),
              ),
              SizedBox(width: AppConstants.sizedBoxHeightMedium),
              CustomOutlinedButton(
                text: "Add Insurance",
                fontWeight: FontWeight.w700,
                borderRadius: AppConstants.borderRadiusSmall,
                foregroundColor: AppConstants.primaryColor,
                borderColor: AppConstants.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _PaymentDetails extends StatelessWidget {
  const _PaymentDetails();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: Text(
            "Payment Details",
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
          ),
        ),
        SizedBox(height: AppConstants.sizedBoxHeightSmall),
        _PaymentAmountRow(title: "Consultation Fee", amount: "AED 45"),
        SizedBox(height: AppConstants.sizedBoxHeightMedium),
        _PaymentAmountRow(title: "VAT", amount: "AED 2"),
        Divider(color: Color(0xFFE6E7EB)),
        _PaymentAmountRow(title: "Subtotal", amount: "AED 47"),
        Divider(color: Color(0xFFE6E7EB)),
      ],
    );
  }
}

class _PaymentAmountRow extends StatelessWidget {
  final String title;
  final String amount;
  const _PaymentAmountRow({required this.title, required this.amount});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 4,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            amount,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}

class _PaymentOption extends StatelessWidget {
  const _PaymentOption();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Payment Options",
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          _PaymentOptionItems(paymentMethod: "Cash on consultation"),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          _PaymentOptionItems(paymentMethod: "Apple pay"),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          _PaymentOptionItems(paymentMethod: "Google pay"),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          _PaymentOptionItems(paymentMethod: "Paypal"),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          _PaymentOptionItems(paymentMethod: "Bkash"),
        ],
      ),
    );
  }
}

class _PaymentOptionItems extends StatelessWidget {
  final String paymentMethod;
  const _PaymentOptionItems({required this.paymentMethod});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 12,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFEDF2FF),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Row(
        children: [
          ReusableSvgImage(assetPath: "assets/icons/common/select_icon.svg"),
          SizedBox(width: AppConstants.sizedBoxHeightSmall),
          Text(
            paymentMethod,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        border: const Border(
          top: BorderSide(
            color: Color(0xFFE6E7EB),
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
      ),
      child: Column(
        children: [
          CustomElevatedButton(
            text: "Pay",
            fontWeight: FontWeight.w700,
            borderRadius: AppConstants.borderRadiusSmall,
            onPressed: () {
              context.go(AppRouter.paymentSuccess);
            },
          ),
        ],
      ),
    );
  }
}
