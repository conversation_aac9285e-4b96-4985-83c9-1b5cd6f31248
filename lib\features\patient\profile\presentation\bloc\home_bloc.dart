import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/user_profile.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/health_score.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/bed_time.dart';
import 'package:imed_fe/features/patient/profile/domain/usecases/get_user_profile.dart';
import 'package:imed_fe/features/patient/profile/domain/usecases/get_health_score.dart';
import 'package:imed_fe/features/patient/profile/domain/usecases/get_bed_time.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetUserProfile getUserProfile;
  final GetHealthScore getHealthScore;
  final GetBedTime getBedTime;

  HomeBloc({
    required this.getUserProfile,
    required this.getHealthScore,
    required this.getBedTime,
  }) : super(HomeInitial()) {
    on<LoadHomeData>(_onLoadHomeData);
  }

  Future<void> _onLoadHomeData(
    LoadHomeData event,
    Emitter<HomeState> emit,
  ) async {
    emit(HomeLoading());

    final userProfileResult = await getUserProfile(NoParams());
    final healthScoreResult = await getHealthScore(NoParams());
    final bedTimeResult = await getBedTime(NoParams());

    // Check if any of the results is a Left (failure)
    if (userProfileResult.isLeft() ||
        healthScoreResult.isLeft() ||
        bedTimeResult.isLeft()) {
      // Get the first failure
      final failure = userProfileResult.fold(
        (failure) => failure,
        (_) => healthScoreResult.fold(
          (failure) => failure,
          (_) => bedTimeResult.fold(
            (failure) => failure,
            (_) =>
                null, // This should never happen as we already checked isLeft()
          ),
        ),
      );

      emit(
        HomeError(message: failure?.message ?? 'An unexpected error occurred'),
      );
      return;
    }

    // All results are Right (success)
    final userProfile = userProfileResult.getOrElse(
      () => throw Exception('This should never happen'),
    );
    final healthScore = healthScoreResult.getOrElse(
      () => throw Exception('This should never happen'),
    );
    final bedTime = bedTimeResult.getOrElse(
      () => throw Exception('This should never happen'),
    );

    emit(
      HomeLoaded(
        userProfile: userProfile,
        healthScore: healthScore,
        bedTime: bedTime,
      ),
    );
  }
}
