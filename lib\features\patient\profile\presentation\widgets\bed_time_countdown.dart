import 'package:flutter/material.dart';

class BedTimeCountdown extends StatelessWidget {
  final String hours;
  final String minutes;
  final Color boxColor;
  final Color textColor;

  const BedTimeCountdown({
    super.key,
    this.hours = '2',
    this.minutes = '27',
    this.boxColor = const Color(0xFF2260FF),
    this.textColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildBox('-'),
        _buildBox(hours),
        _buildBox('H'),
        _buildLetterBox(':'),
        _buildBox(minutes),
        _buildBox('M'),
      ],
    );
  }

  Widget _buildBox(String text) {
    return Container(
      // padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      alignment: Alignment.center,
      width: 30,
      height: 50,

      decoration: BoxDecoration(
        color: boxColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w700,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildLetterBox(String letter) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        letter,
        style: TextStyle(fontWeight: FontWeight.bold, color: boxColor),
      ),
    );
  }
}
