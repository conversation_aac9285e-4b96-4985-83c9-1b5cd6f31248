import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class Doctor<PERSON><PERSON> extends StatelessWidget {
  final String title;
  final String buttonText;
  final VoidCallback? onTap;
  final Widget? image;

  final Color buttonColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry padding;

  const DoctorCard({
    super.key,
    this.title = 'Looking for a doctor nearby?',
    this.buttonText = 'Tap here',
    this.onTap,
    this.image,

    this.buttonColor = const Color(0xFF2563EB),
    this.borderRadius,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFEFF4FA), Color(0xFFF3F6FD)],
        ),
        borderRadius: borderRadius ?? BorderRadius.circular(16),
      ),
      padding: padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          image ?? Image.asset('assets/icons/doctor.png'),
          const Gap(12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
              const Gap(4),
              GestureDetector(
                onTap: onTap,
                child: Text(
                  buttonText,
                  style: TextStyle(
                    color: buttonColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          Image.asset('assets/icons/search_shadow_icon.png'),
        ],
      ),
    );
  }
}
