import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_icon_button.dart';

class SocialLoginSection extends StatelessWidget {
  const SocialLoginSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'Or sign in with',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppConstants.textMidColor),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ReusableIconButton(
              iconPath: 'assets/icons/auth/google_icon.svg',
              onPressed: () {},
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightMedium),
            ReusableIconButton(
              iconPath: 'assets/icons/auth/facebook_icon.svg',
              onPressed: () {},
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightMedium),
            ReusableIconButton(
              iconPath: 'assets/icons/auth/microsoft_icon.svg',
              onPressed: () {},
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightMedium),
            ReusableIconButton(
              iconPath: 'assets/icons/auth/fingerprint_icon.svg',
              onPressed: () {},
            ),
          ],
        ),
      ],
    );
  }
}
