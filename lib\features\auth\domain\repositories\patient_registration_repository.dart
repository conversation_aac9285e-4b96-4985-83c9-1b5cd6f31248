import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';

abstract class PatientRegistrationRepository {
  Future<Either<Failure, PatientRegistrationResponse>> registerPatient(
    PatientRegistrationRequest request,
  );
}
