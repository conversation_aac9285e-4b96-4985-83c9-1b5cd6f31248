import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/utils/validation_utils.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_icon_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/core/widgets/custom_text_field.dart';
import 'package:imed_fe/features/auth/presentation/widgets/social_login_section.dart';

import 'package:imed_fe/features/auth/presentation/pages/signup_page.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _handleLogin() {
    context.push(AppRouter.patientProfile);
    if (_formKey.currentState!.validate()) {}
  }

  void _handleForgotPassword() {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: ReusableSvgImage(assetPath: 'assets/icons/left.svg'),
        title: const Text('Sign in'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                Text(
                  'Welcome',
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                Text(
                  "Email or Mobile number",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),

                const SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildEmailField(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Text(
                  'Password',

                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildPasswordField(),

                const SizedBox(height: AppConstants.sizedBoxHeightSmall),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: _handleForgotPassword,
                    child: const Text('Forgot password'),
                  ),
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                CustomElevatedButton(onPressed: _handleLogin, text: 'Sign in'),
                const SizedBox(
                  height: AppConstants.sizedBoxHeightExtraLarge + 50,
                ),
                const SocialLoginSection(),
                const SizedBox(
                  height: AppConstants.sizedBoxHeightExtraLarge + 50,
                ),
                _buildSignUpSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  CustomTextField _buildPasswordField() {
    return CustomTextField(
      controller: _passwordController,
      hintText: 'Enter your password',
      keyboardType: TextInputType.visiblePassword,
      obscureText: _obscurePassword,
      validator: ValidationUtils.validatePassword,
      suffixIcon: ReusableIconButton(
        icon: _obscurePassword ? Icons.visibility_off : Icons.visibility,
        onPressed: _togglePasswordVisibility,
        color: AppConstants.textMidColor,
      ),
    );
  }

  CustomTextField _buildEmailField() {
    return CustomTextField(
      controller: _emailController,
      hintText: 'Enter your email or mobile number',

      keyboardType: TextInputType.emailAddress,
      validator: ValidationUtils.validateEmail,
    );
  }

  Widget _buildSignUpSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text("Don't have an account?"),
        TextButton(
          onPressed: () {
            context.push(AppRouter.signup);
          },
          child: const Text(
            'Sign up',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }
}
