import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/repositories/auth_repository.dart';

class ConfirmResetPassword implements UseCase<void, ConfirmResetPasswordParams> {
  final AuthRepository repository;

  ConfirmResetPassword(this.repository);

  @override
  Future<Either<Failure, void>> call(ConfirmResetPasswordParams params) async {
    return await repository.confirmResetPassword(
      token: params.token,
      newPassword: params.newPassword,
    );
  }
}

class ConfirmResetPasswordParams extends Equatable {
  final String token;
  final String newPassword;

  const ConfirmResetPasswordParams({
    required this.token,
    required this.newPassword,
  });

  @override
  List<Object> get props => [token, newPassword];
}