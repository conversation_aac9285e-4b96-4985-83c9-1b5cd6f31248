import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';

import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/bloc/doctor_bloc.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_map_marker.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_type.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_type_bottom_sheet.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/radius_bottom_sheet.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_search_map.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_type_filter.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/search_radius_selector.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_found_results_widget.dart';

class NearbyDoctorSearchListPage extends StatefulWidget {
  const NearbyDoctorSearchListPage({super.key});

  @override
  State<NearbyDoctorSearchListPage> createState() =>
      _NearbyDoctorSearchListPageState();
}

class _NearbyDoctorSearchListPageState
    extends State<NearbyDoctorSearchListPage> {
  final Completer<GoogleMapController> _googleMapControllerCompleter =
      Completer();
  GoogleMapController? _mapController; // To control map animations

  Set<Marker> _markers = {};
  Doctor?
  _selectedDoctorOnMap; // To highlight marker or show info window if needed

  DoctorType _selectedDoctorType = const DoctorType(
    name: "Family Doctor",
    assetPath: 'assets/icons/patient/family.svg',
    doctorCount: 0,
  );
  double _selectedRadiusKm = 6.0;

  static final List<DoctorType> _doctorTypesForFilter = [
    const DoctorType(
      name: 'Family Doctor',
      assetPath: 'assets/icons/patient/family.svg',
      doctorCount: 8,
    ),
    const DoctorType(
      name: 'Medicine',
      assetPath: 'assets/icons/patient/medicine.svg',
      doctorCount: 8,
    ),
    const DoctorType(
      name: 'Dentist',
      assetPath: 'assets/icons/patient/dentist.svg',
      doctorCount: 21,
    ),
  ];

  static const LatLng _defaultInitialLocation = LatLng(23.8103, 90.4125);

  @override
  void initState() {
    super.initState();
    _loadNearbyDoctors();
  }

  void _loadNearbyDoctors() {
    context.read<DoctorBloc>().add(
      LoadNearbyDoctors(
        latitude:
            _defaultInitialLocation
                .latitude, // Replace with actual/dynamic location
        longitude:
            _defaultInitialLocation
                .longitude, // Replace with actual/dynamic location
        radius: _selectedRadiusKm,
        // doctorType: _selectedDoctorType.name, // Add if API supports
      ),
    );
  }

  Future<void> _createMarkers(List<Doctor> doctors) async {
    _markers = await DoctorMapMarker.createMarkers(
      doctors: doctors,
      onTap: _onDoctorMarkerTap,
    );
    if (mounted) {
      setState(() {});
    }
  }

  void _onDoctorMarkerTap(Doctor doctor) {
    setState(() {
      context.go(AppRouter.doctorProfileView);
    });
  }

  Future<void> _animateToDoctorLocation(LatLng location) async {
    final controller = await _googleMapControllerCompleter.future;
    controller.animateCamera(CameraUpdate.newLatLngZoom(location, 15.0));
  }

  void _handleDoctorTypeSelected(String? typeName) {
    // Changed to String?
    if (typeName == null) return; // Handle null case

    final selectedType = _doctorTypesForFilter.firstWhere(
      (type) => type.name == typeName,
      orElse: () => _selectedDoctorType,
    );
    if (mounted) {
      setState(() {
        _selectedDoctorType = selectedType;
      });
    }
    _loadNearbyDoctors();
  }

  void _handleRadiusSelected(double radiusInKm) {
    if (mounted) {
      setState(() {
        _selectedRadiusKm = radiusInKm;
      });
    }
    _loadNearbyDoctors();
  }

  @override
  Widget build(BuildContext context) {
    // Create an instance of DoctorTypeFilter to access its method
    // This instance is not strictly needed if DoctorTypeBottomSheet.show is used directly
    // final doctorTypeFilterInstance = DoctorTypeFilter(
    //   selectedType: _selectedDoctorType.name,
    //   doctorTypes: _doctorTypesForFilter.map((dt) => dt.name).toList(),
    //   onTypeSelected: _handleDoctorTypeSelected,
    //   onEdit: () {}, // This onEdit will be replaced by direct call
    // );

    return Scaffold(
      appBar: AppBar(
        leading:
            GoRouter.of(context).canPop()
                ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: () => GoRouter.of(context).pop(),
                )
                : null,
        title: const Text('Doctors near by'),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        titleTextStyle: const TextStyle(
          color: Colors.black,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      body: BlocConsumer<DoctorBloc, DoctorState>(
        listener: (context, state) {
          if (state is NearbyDoctorsLoaded) {
            _createMarkers(state.doctors);
          }
        },
        builder: (context, state) {
          if (state is DoctorLoading && _markers.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is DoctorError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${state.message}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadNearbyDoctors,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          List<Doctor> currentDoctors =
              (state is NearbyDoctorsLoaded) ? state.doctors : [];

          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: Column(
                  children: [
                    DoctorTypeFilter(
                      selectedType: _selectedDoctorType.name,
                      doctorTypes:
                          _doctorTypesForFilter.map((dt) => dt.name).toList(),
                      onTypeSelected: _handleDoctorTypeSelected,
                      onEdit: () {
                        DoctorTypeBottomSheet.show(
                          context: context,
                          selectedDoctorType: _selectedDoctorType.name,
                          onTypeSelected: _handleDoctorTypeSelected,
                          // The DoctorTypeBottomSheet internally has its own list of DoctorType objects.
                          // If you need to pass a dynamic list from this page,
                          // DoctorTypeBottomSheet's static `show` method and constructor
                          // would need to be updated to accept `List<DoctorType>`.
                          // For now, it uses its internal static list.
                        );
                      },
                    ),
                    const SizedBox(height: 10),
                    SearchRadiusSelector(
                      radius: _selectedRadiusKm,
                      onRadiusChanged: _handleRadiusSelected,
                      radiusType: RadiusType.suggested,
                      onRadiusTypeChanged: (RadiusType type) {
                        // Potentially handle state change if radius type (suggested/custom) is managed here
                      },
                      onEdit: () {
                        RadiusBottomSheet.show(
                          context: context,
                          selectedRadius:
                              _selectedRadiusKm * 1000, // Pass in meters
                          onRadiusSelected: (double radiusInMeters) {
                            _handleRadiusSelected(
                              radiusInMeters / 1000,
                            ); // Convert back to KM
                          },
                        );
                      },
                      isCompact: true,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Stack(
                  children: [
                    DoctorSearchMap(
                      initialLocation: _defaultInitialLocation,
                      markers: _markers,
                      onMapCreated: (GoogleMapController controller) {
                        if (!_googleMapControllerCompleter.isCompleted) {
                          _googleMapControllerCompleter.complete(controller);
                        }
                        _mapController = controller;
                      },
                    ),
                    GestureDetector(
                      onTap: () => context.push(AppRouter.doctorProfileView),
                      child: DoctorFoundResultsWidget(
                        doctors: currentDoctors,
                        doctorState: state,
                        onDoctorTap: _onDoctorMarkerTap,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
