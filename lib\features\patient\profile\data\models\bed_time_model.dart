import 'package:imed_fe/features/patient/profile/domain/entities/bed_time.dart';

class BedTimeModel extends BedTime {
  const BedTimeModel({
    required int hours,
    required int minutes,
    required String message,
  }) : super(hours: hours, minutes: minutes, message: message);

  factory BedTimeModel.fromJson(Map<String, dynamic> json) {
    return BedTimeModel(
      hours: json['hours'] ?? 0,
      minutes: json['minutes'] ?? 0,
      message: json['message'] ?? 'Time to sleep!',
    );
  }

  Map<String, dynamic> toJson() {
    return {'hours': hours, 'minutes': minutes, 'message': message};
  }
}
