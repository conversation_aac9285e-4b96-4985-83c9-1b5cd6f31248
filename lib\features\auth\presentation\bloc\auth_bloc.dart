import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/entities/auth_credentials.dart';
import 'package:imed_fe/features/auth/domain/entities/registration_data.dart';
import 'package:imed_fe/features/auth/domain/entities/user.dart';
import 'package:imed_fe/features/auth/domain/usecases/confirm_reset_password.dart';
import 'package:imed_fe/features/auth/domain/usecases/get_current_user.dart';
import 'package:imed_fe/features/auth/domain/usecases/reset_password.dart';
import 'package:imed_fe/features/auth/domain/usecases/sign_in.dart';
import 'package:imed_fe/features/auth/domain/usecases/sign_out.dart';
import 'package:imed_fe/features/auth/domain/usecases/sign_up.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final SignIn signIn;
  final SignUp signUp;
  final SignOut signOut;
  final ResetPassword resetPassword;
  final ConfirmResetPassword confirmResetPassword;
  final GetCurrentUser getCurrentUser;

  AuthBloc({
    required this.signIn,
    required this.signUp,
    required this.signOut,
    required this.resetPassword,
    required this.confirmResetPassword,
    required this.getCurrentUser,
  }) : super(AuthInitial()) {
    on<CheckAuthStatus>(_onCheckAuthStatus);
    on<SignInRequested>(_onSignInRequested);
    on<SignUpRequested>(_onSignUpRequested);
    on<SignOutRequested>(_onSignOutRequested);
    on<ResetPasswordRequested>(_onResetPasswordRequested);
    on<ConfirmResetPasswordRequested>(_onConfirmResetPasswordRequested);
  }

  Future<void> _onCheckAuthStatus(CheckAuthStatus event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await getCurrentUser(NoParams());
    result.fold(
      (failure) => emit(AuthUnauthenticated()),
      (user) => user != null ? emit(AuthAuthenticated(user: user)) : emit(AuthUnauthenticated()),
    );
  }

  Future<void> _onSignInRequested(SignInRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await signIn(event.credentials);
    result.fold(
      (failure) => emit(AuthError(message: 'Failed to sign in')),
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  Future<void> _onSignUpRequested(SignUpRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await signUp(event.registrationData);
    result.fold(
      (failure) => emit(AuthError(message: 'Failed to sign up')),
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }

  Future<void> _onSignOutRequested(SignOutRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await signOut(NoParams());
    result.fold(
      (failure) => emit(AuthError(message: 'Failed to sign out')),
      (_) => emit(AuthUnauthenticated()),
    );
  }

  Future<void> _onResetPasswordRequested(ResetPasswordRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await resetPassword(event.email);
    result.fold(
      (failure) => emit(AuthError(message: 'Failed to reset password')),
      (_) => emit(const PasswordResetSent()),
    );
  }

  Future<void> _onConfirmResetPasswordRequested(ConfirmResetPasswordRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await confirmResetPassword(
      ConfirmResetPasswordParams(
        token: event.token,
        newPassword: event.newPassword,
      ),
    );
    result.fold(
      (failure) => emit(AuthError(message: 'Failed to confirm password reset')),
      (_) => emit(const PasswordResetConfirmed()),
    );
  }
}