import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  void _handleSignIn() {
    context.push(AppRouter.signin);
  }

  void _handleSignUp() {
    context.go(AppRouter.signup);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2B60E9), Color(0xFF56B3FF)],
          ),
        ),
        child: Column(
          children: [
            Spacer(flex: 1),
            ReusableSvgImage(assetPath: 'assets/images/imed_logo.svg'),
            const SizedBox(height: 16),
            Text(
              'Find Your Doctor',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                color: AppConstants.backgroundColor,
              ),
            ),

            Spacer(flex: 2),

            // Buttons
            CustomElevatedButton(
              onPressed: () {
                _handleSignIn();
              },
              text: 'Sign in',

              backgroundColor: Colors.white,
              foregroundColor: Colors.blue,
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                _handleSignUp();
              },
              child: Text(
                'Register',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: AppConstants.backgroundColor,
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
