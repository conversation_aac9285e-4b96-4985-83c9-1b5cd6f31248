import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';

class DoctorMapMarker {
  static Future<BitmapDescriptor> createCustomMarkerBitmap() async {
    return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
  }

  static Future<Marker> createMarker({
    required Doctor doctor,
    required Function(Doctor) onTap,
  }) async {
    final customIcon = await createCustomMarkerBitmap();

    return Marker(
      markerId: MarkerId(doctor.id),
      position: LatLng(doctor.location.latitude, doctor.location.longitude),
      icon: customIcon,
      infoWindow: InfoWindow(
        title: doctor.name,
        snippet: doctor.specialty,
        onTap: () => onTap(doctor),
      ),
    );
  }

  static Future<Set<Marker>> createMarkers({
    required List<Doctor> doctors,
    required Function(Doctor) onTap,
  }) async {
    final markers = <Marker>{};

    for (final doctor in doctors) {
      final marker = await createMarker(doctor: doctor, onTap: onTap);
      markers.add(marker);
    }

    return markers;
  }
}
