import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_availability_model.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_location_model.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';

class Doctor<PERSON>odel extends Doctor {
  const DoctorModel({
    required super.id,
    required super.name,
    required super.specialty,
    required super.imageUrl,
    required super.rating,
    required super.reviewCount,
    required super.about,
    required super.phoneNumber,
    required super.email,
    required DoctorLocationModel super.location,
    required DoctorAvailabilityModel super.availability,
    required super.consultationFee,
    required super.services,
    required super.languages,
    required super.education,
    required super.experience,
    super.hospitalName = '',
    super.distance = 0.0,
    super.isAvailableForSchedule = false,
    super.certifications = const [],
    super.yearsOfExperience = 0,
    super.isVerified = false,
  });

  factory DoctorModel.fromJson(Map<String, dynamic> json) {
    return DoctorModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      specialty: json['specialty'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      about: json['about'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
      location: DoctorLocationModel.fromJson(json['location'] ?? {}),
      availability: DoctorAvailabilityModel.fromJson(
        json['availability'] ?? {},
      ),
      consultationFee: (json['consultationFee'] ?? 0.0).toDouble(),
      services: List<String>.from(json['services'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      education: json['education'] ?? '',
      experience: json['experience'] ?? '',
      hospitalName: json['hospitalName'] ?? '',
      distance: (json['distance'] ?? 0.0).toDouble(),
      isAvailableForSchedule: json['isAvailableForSchedule'] ?? false,
      certifications: List<String>.from(json['certifications'] ?? []),
      yearsOfExperience: json['yearsOfExperience'] ?? 0,
      isVerified: json['isVerified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'specialty': specialty,
      'imageUrl': imageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'about': about,
      'phoneNumber': phoneNumber,
      'email': email,
      'location': (location as DoctorLocationModel).toJson(),
      'availability': (availability as DoctorAvailabilityModel).toJson(),
      'consultationFee': consultationFee,
      'services': services,
      'languages': languages,
      'education': education,
      'experience': experience,
      'hospitalName': hospitalName,
      'distance': distance,
      'isAvailableForSchedule': isAvailableForSchedule,
      'certifications': certifications,
      'yearsOfExperience': yearsOfExperience,
      'isVerified': isVerified,
    };
  }
}
