import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class GetDoctorAvailability
    implements UseCase<DoctorAvailability, DoctorAvailabilityParams> {
  final DoctorRepository repository;

  GetDoctorAvailability(this.repository);

  @override
  Future<Either<Failure, DoctorAvailability>> call(
    DoctorAvailabilityParams params,
  ) async {
    try {
      final result = await repository.getDoctorAvailability(params.doctorId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}

class DoctorAvailabilityParams extends Equatable {
  final String doctorId;

  const DoctorAvailabilityParams({required this.doctorId});

  @override
  List<Object?> get props => [doctorId];
}
