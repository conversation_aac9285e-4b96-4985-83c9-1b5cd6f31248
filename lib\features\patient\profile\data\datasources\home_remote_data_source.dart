import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/patient/profile/data/models/user_profile_model.dart';
import 'package:imed_fe/features/patient/profile/data/models/health_score_model.dart';
import 'package:imed_fe/features/patient/profile/data/models/bed_time_model.dart';

abstract class HomeRemoteDataSource {
  Future<UserProfileModel> getUserProfile();
  Future<HealthScoreModel> getHealthScore();
  Future<BedTimeModel> getBedTime();
}

class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  final DioClient dioClient;

  HomeRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<UserProfileModel> getUserProfile() async {
    try {
      final response = await dioClient.get('/user/profile');
      return UserProfileModel.fromJson(response.data);
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return const UserProfileModel(
        name: '<PERSON><PERSON>',
        avatarUrl: 'assets/images/avatar.png',
      );
    }
  }

  @override
  Future<HealthScoreModel> getHealthScore() async {
    try {
      final response = await dioClient.get('/user/health-score');
      return HealthScoreModel.fromJson(response.data);
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return const HealthScoreModel(score: 85, status: 'Good');
    }
  }

  @override
  Future<BedTimeModel> getBedTime() async {
    try {
      final response = await dioClient.get('/user/bed-time');
      return BedTimeModel.fromJson(response.data);
    } catch (e) {
      // For demo purposes, return mock data if API fails
      return const BedTimeModel(
        hours: 2,
        minutes: 27,
        message: "It's almost time for bed",
      );
    }
  }
}
