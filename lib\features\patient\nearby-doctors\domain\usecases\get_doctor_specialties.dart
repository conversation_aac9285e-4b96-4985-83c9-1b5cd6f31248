import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class GetDoctorSpecialties implements UseCase<List<String>, NoParams> {
  final DoctorRepository repository;

  GetDoctorSpecialties(this.repository);

  @override
  Future<Either<Failure, List<String>>> call(NoParams params) async {
    try {
      final result = await repository.getDoctorSpecialties();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
