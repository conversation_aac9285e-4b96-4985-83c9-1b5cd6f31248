import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';

class ReviewSection extends StatelessWidget {
  const ReviewSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reviews'),
        elevation: 0,
        backgroundColor: AppConstants.backgroundColor,
        foregroundColor: AppConstants.textPrimaryColor,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                vertical: AppConstants.paddingMedium,
                horizontal: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppConstants.borderColor2,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '5.0',
                        style: TextStyle(
                          fontSize: 30,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textHighColor,
                        ),
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightSmall / 2),
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 15),
                          Icon(Icons.star, color: Colors.amber, size: 15),
                          Icon(Icons.star, color: Colors.amber, size: 15),
                          Icon(Icons.star, color: Colors.amber, size: 15),
                          Icon(Icons.star, color: Colors.amber, size: 15),
                        ],
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightSmall / 2), // 4.0
                      Text(
                        '(1,254)',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium, // 16.0
                          color: AppConstants.textMidColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: AppConstants.paddingLarge), // 24.0
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildRatingBar(5, 1.0),
                        _buildRatingBar(4, 0.8),
                        _buildRatingBar(3, 0.6),
                        _buildRatingBar(2, 0.4),
                        _buildRatingBar(1, 0.2),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium), // 16.0
              child: Column(
                children: [
                  const ReviewCard(
                    profileImage: 'assets/images/user_pic.png',
                    name: 'MD Anisur Rahman',
                    time: '1 Hour Ago',
                    rating: 5.0,
                    review: 'I\'ve been using iMed for months and now have my primary care physicians through them. Great doctors, great service. Fair price, quick, and best of all I didn\'t have to leave the comfort of my home.',
                  ),
                  const DottedLineSeparator(),
                  const ReviewCard(
                    profileImage: 'assets/images/user_pic.png',
                    name: 'MD Anisur Rahman',
                    time: '1 Hour Ago',
                    rating: 5.0,
                    review: 'Great Doctor. Great response, Very well treatment recieved',
                  ),
                  const DottedLineSeparator(),
                  const ReviewCard(
                    profileImage: 'assets/images/user_pic.png',
                    name: 'MD Anisur Rahman',
                    time: '2 Hours Ago',
                    rating: 5.0,
                    review: 'Excellent service and very professional. Highly recommended!',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.sizedBoxHeightSmall / 4), // 2.0
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Row(
              children: List.generate(stars, (index) {
                return const Icon(Icons.star, color: Colors.amber, size: AppConstants.fontSizeMedium);
              }),
            ),
          ),
          const SizedBox(width: AppConstants.sizedBoxHeightSmall / 4),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: AppConstants.borderColor2,
              valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
              minHeight: AppConstants.sizedBoxHeightSmall,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall / 2),
            ),
          ),
        ],
      ),
    );
  }
}

class DottedLineSeparator extends StatelessWidget {
  const DottedLineSeparator({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium), // 16.0
      height: 1.0, // Keeping 1.0
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final double dashWidth = 4.0; // Keeping 4.0
          final double dashSpace = 2.0; // Keeping 2.0
          final int dashCount = (constraints.maxWidth / (dashWidth + dashSpace)).floor();
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(dashCount, (_) {
              return Container(
                width: dashWidth,
                height: 1.0,
                color: AppConstants.borderColor2,
              );
            }),
          );
        },
      ),
    );
  }
}

class ReviewCard extends StatelessWidget {
  final String profileImage;
  final String name;
  final String time;
  final double rating;
  final String review;

  const ReviewCard({
    super.key,
    required this.profileImage,
    required this.name,
    required this.time,
    required this.rating,
    required this.review,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium), // 16.0
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall), // 8.0
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 50, // Keeping 50.0
                height: 50, // Keeping 50.0
                margin: const EdgeInsets.only(right: AppConstants.sizedBoxHeightMedium / 1.33), // Approx 12.0
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: AssetImage(profileImage),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeLarge, // 18.0
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textHighColor,
                      ),
                    ),
                    const SizedBox(height: AppConstants.sizedBoxHeightSmall / 2), // 4.0
                    Text(
                      time,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall, // 12.0 (closest to 14.0)
                        color: AppConstants.textMidColor,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    rating.toStringAsFixed(1),
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.textHighColor,
                    ),
                  ),
                  const SizedBox(height: AppConstants.sizedBoxHeightSmall / 2),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: AppConstants.fontSizeMedium,
                      );
                    }),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightMedium), // 16.0
          Text(
            review,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium, // 16.0
              color: AppConstants.textHighColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
