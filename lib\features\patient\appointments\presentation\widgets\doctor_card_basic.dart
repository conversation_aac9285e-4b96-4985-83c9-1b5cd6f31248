import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/profile_pic.dart';

class DoctorCardBasic extends StatelessWidget {
  final String name;
  final String degree;
  final String specialty;
  final String imageUrl;

  const DoctorCardBasic({
    super.key,
    required this.name,
    required this.specialty,
    required this.degree,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ProfilePic(
              imageUrl: imageUrl,
              width: 66,
              height: 88,
              borderRadius: 8,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightMedium),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Text(
                    degree,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: AppConstants.textMidColor,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Text(
                    specialty,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
