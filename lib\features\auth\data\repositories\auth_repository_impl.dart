import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/network/network_info.dart';
import 'package:imed_fe/features/auth/data/datasources/auth_local_data_source.dart';
import 'package:imed_fe/features/auth/data/datasources/auth_remote_data_source.dart';
import 'package:imed_fe/features/auth/domain/entities/auth_credentials.dart';
import 'package:imed_fe/features/auth/domain/entities/registration_data.dart';
import 'package:imed_fe/features/auth/domain/entities/user.dart';
import 'package:imed_fe/features/auth/domain/repositories/auth_repository.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, User>> signIn(AuthCredentials credentials) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signIn(
          credentials.email,
          credentials.password,
        );
        await localDataSource.cacheUser(user);
        return Right(user);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, User>> signUp(RegistrationData registrationData) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signUp(
          registrationData.fullName,
          registrationData.email,
          registrationData.password,
          registrationData.phoneNumber,
          registrationData.dateOfBirth,
          registrationData.gender,
        );
        await localDataSource.cacheUser(user);
        return Right(user);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.signOut();
        await localDataSource.clearUser();
        return const Right(null);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      // Even if offline, we can still clear local data
      await localDataSource.clearUser();
      return const Right(null);
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(String email) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.resetPassword(email);
        return const Right(null);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, void>> confirmResetPassword({
    required String token,
    required String newPassword,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.confirmResetPassword(
          token: token,
          newPassword: newPassword,
        );
        return const Right(null);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      // First try to get from local cache
      final localUser = await localDataSource.getLastUser();
      if (localUser != null) {
        return Right(localUser);
      }

      // If not in cache and online, try to get from server
      if (await networkInfo.isConnected) {
        final remoteUser = await remoteDataSource.getCurrentUser();
        if (remoteUser != null) {
          await localDataSource.cacheUser(remoteUser);
          return Right(remoteUser);
        }
      }

      // No user found
      return const Right(null);
    } on Exception {
      return Left(CacheFailure());
    }
  }
}