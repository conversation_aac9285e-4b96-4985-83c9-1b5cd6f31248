import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/repositories/doctor_repository.dart';

class FilterDoctors implements UseCase<List<Doctor>, FilterDoctorsParams> {
  final DoctorRepository repository;

  FilterDoctors(this.repository);

  @override
  Future<Either<Failure, List<Doctor>>> call(FilterDoctorsParams params) async {
    try {
      final result = await repository.filterDoctors(
        latitude: params.latitude,
        longitude: params.longitude,
        radius: params.radius,
        specialty: params.specialty,
        minRating: params.minRating,
        isAvailableForSchedule: params.isAvailableForSchedule,
        searchQuery: params.searchQuery,
      );
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}

class FilterDoctorsParams extends Equatable {
  final double latitude;
  final double longitude;
  final double radius;
  final String? specialty;
  final double? minRating;
  final bool? isAvailableForSchedule;
  final String? searchQuery;

  const FilterDoctorsParams({
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.specialty,
    this.minRating,
    this.isAvailableForSchedule,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    latitude,
    longitude,
    radius,
    specialty,
    minRating,
    isAvailableForSchedule,
    searchQuery,
  ];
}
