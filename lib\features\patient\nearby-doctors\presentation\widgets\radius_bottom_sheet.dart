import 'package:flutter/material.dart';

class RadiusBottomSheet extends StatefulWidget {
  final double selectedRadius; // in meters
  final Function(double) onRadiusSelected; // returns meters
  final List<double> radiusOptions; // in meters

  const RadiusBottomSheet({
    super.key,
    required this.selectedRadius,
    required this.onRadiusSelected,
    this.radiusOptions = const [
      500,
      1000,
      2000,
      5000,
      10000,
    ], // Default in meters
  });

  static void show({
    required BuildContext context,
    required double selectedRadius, // in meters
    required Function(double) onRadiusSelected, // returns meters
    List<double> radiusOptions = const [
      500,
      1000,
      2000,
      5000,
      10000,
    ], // Default in meters
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Important for taller bottom sheets
      builder:
          (context) => RadiusBottomSheet(
            selectedRadius: selectedRadius,
            onRadiusSelected: onRadiusSelected,
            radiusOptions: radiusOptions,
          ),
    );
  }

  @override
  State<RadiusBottomSheet> createState() => _RadiusBottomSheetState();
}

class _RadiusBottomSheetState extends State<RadiusBottomSheet> {
  late bool _isCustomRadius;
  late double _currentSelectedRadiusMeters;
  late double _currentCustomRadiusKm;

  @override
  void initState() {
    super.initState();
    _currentSelectedRadiusMeters = widget.selectedRadius;
    // Check if the current selectedRadius is one of the predefined options
    _isCustomRadius = !widget.radiusOptions.contains(widget.selectedRadius);
    if (_isCustomRadius) {
      _currentCustomRadiusKm = widget.selectedRadius / 1000.0;
    } else {
      // Default custom slider value if not initially custom
      _currentCustomRadiusKm =
          widget.radiusOptions.isNotEmpty
              ? (widget.radiusOptions.last / 1000.0)
              : 5.0; // Default to 5km if options are empty
    }
  }

  double _metersToKm(double meters) => meters / 1000.0;
  double _kmToMeters(double km) => km * 1000.0;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Selected search radius',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Column(
              mainAxisAlignment:
                  MainAxisAlignment.start, // Align items to the start
              children: [
                InkWell(
                  onTap: () {
                    if (_isCustomRadius) {
                      // Only update if changing state
                      setState(() {
                        _isCustomRadius = false;
                        if (widget.radiusOptions.isNotEmpty) {
                          // Select the first suggested if available, or keep current if it's a suggested one
                          if (!widget.radiusOptions.contains(
                            _currentSelectedRadiusMeters,
                          )) {
                            _currentSelectedRadiusMeters =
                                widget.radiusOptions.first;
                          }
                        }
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Suggested radius'),
                      Radio<bool>(
                        value: false, // Suggested radius
                        groupValue: _isCustomRadius,
                        onChanged: (bool? value) {
                          if (value != null && !value) {
                            setState(() {
                              _isCustomRadius = false;
                              if (widget.radiusOptions.isNotEmpty) {
                                if (!widget.radiusOptions.contains(
                                  _currentSelectedRadiusMeters,
                                )) {
                                  _currentSelectedRadiusMeters =
                                      widget.radiusOptions.first;
                                }
                              }
                            });
                          }
                        },
                        activeColor: const Color(0xFF2260FF),
                      ),
                    ],
                  ),
                ),
                // Spacing between the two radio options
                InkWell(
                  onTap: () {
                    if (!_isCustomRadius) {
                      // Only update if changing state
                      setState(() {
                        _isCustomRadius = true;
                        // When switching to custom, current selected becomes the custom slider's value
                        _currentSelectedRadiusMeters = _kmToMeters(
                          _currentCustomRadiusKm,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Custom radius'),
                      Radio<bool>(
                        value: true, // Custom radius
                        groupValue: _isCustomRadius,
                        onChanged: (bool? value) {
                          if (value != null && value) {
                            setState(() {
                              _isCustomRadius = true;
                              _currentSelectedRadiusMeters = _kmToMeters(
                                _currentCustomRadiusKm,
                              );
                            });
                          }
                        },
                        activeColor: const Color(0xFF2260FF),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (!_isCustomRadius)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    widget.radiusOptions.map((radiusMeters) {
                      final radiusKm = _metersToKm(radiusMeters);
                      final isSelected =
                          !_isCustomRadius &&
                          _currentSelectedRadiusMeters == radiusMeters;
                      return InkWell(
                        onTap: () {
                          setState(() {
                            _isCustomRadius = false;
                            _currentSelectedRadiusMeters = radiusMeters;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? const Color(0xFF2260FF).withOpacity(0.1)
                                    : Colors.grey[200],
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? const Color(0xFF2260FF)
                                      : Colors.transparent,
                              width: 1.5,
                            ),
                          ),
                          child: Text(
                            '${radiusKm.toStringAsFixed(radiusKm.truncateToDouble() == radiusKm ? 0 : 1)} KM',
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? const Color(0xFF2260FF)
                                      : Colors.black87,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            if (_isCustomRadius)
              Row(
                children: [
                  Expanded(
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: const Color(0xFF2260FF),
                        inactiveTrackColor: const Color(
                          0xFF2260FF,
                        ).withOpacity(0.3),
                        thumbColor: const Color(0xFF2260FF),
                        overlayColor: const Color(0xFF2260FF).withOpacity(0.2),
                        valueIndicatorColor: const Color(0xFF2260FF),
                        valueIndicatorTextStyle: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      child: Slider(
                        value: _currentCustomRadiusKm,
                        min: 0.1, // 100 meters
                        max: 50.0, // 50 km
                        divisions:
                            499, // (50 - 0.1) / 0.1 + 1 for 100m steps approx
                        label:
                            '${_currentCustomRadiusKm.toStringAsFixed(1)} KM',
                        onChanged: (double value) {
                          setState(() {
                            _currentCustomRadiusKm = value;
                            _currentSelectedRadiusMeters = _kmToMeters(
                              _currentCustomRadiusKm,
                            );
                          });
                        },
                      ),
                    ),
                  ),
                  Text(
                    '${_currentCustomRadiusKm.toStringAsFixed(1)} KM',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: FilledButton(
                onPressed: () {
                  widget.onRadiusSelected(_currentSelectedRadiusMeters);
                  Navigator.pop(context);
                },
                style: FilledButton.styleFrom(
                  backgroundColor: const Color(0xFF2260FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Search',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ), // Padding for safe area if keyboard appears
          ],
        ),
      ),
    );
  }
}
