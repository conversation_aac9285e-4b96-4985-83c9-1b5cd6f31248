part of 'doctor_bloc.dart';

abstract class Doctor<PERSON><PERSON> extends Equatable {
  const DoctorEvent();

  @override
  List<Object?> get props => [];
}

class LoadNearbyDoctors extends Doctor<PERSON>vent {
  final double latitude;
  final double longitude;
  final double radius;
  final String? specialty;

  const LoadNearbyDoctors({
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.specialty,
  });

  @override
  List<Object?> get props => [latitude, longitude, radius, specialty];
}

class LoadDoctorDetails extends DoctorEvent {
  final String doctorId;

  const LoadDoctorDetails({required this.doctorId});

  @override
  List<Object?> get props => [doctorId];
}

class LoadDoctorAvailability extends DoctorEvent {
  final String doctorId;

  const LoadDoctorAvailability({required this.doctorId});

  @override
  List<Object?> get props => [doctorId];
}

class FilterDoctors extends DoctorEvent {
  final double latitude;
  final double longitude;
  final double radius;
  final String? specialty;
  final double? minRating;
  final bool? isAvailableForSchedule;
  final String? searchQuery;

  const FilterDoctors({
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.specialty,
    this.minRating,
    this.isAvailableForSchedule,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    latitude,
    longitude,
    radius,
    specialty,
    minRating,
    isAvailableForSchedule,
    searchQuery,
  ];
}

class LoadDoctorSpecialties extends DoctorEvent {
  const LoadDoctorSpecialties();

  @override
  List<Object?> get props => [];
}
