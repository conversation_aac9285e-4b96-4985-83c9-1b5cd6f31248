part of 'doctor_bloc.dart';

abstract class Doctor<PERSON><PERSON> extends Equatable {
  const DoctorState();

  @override
  List<Object?> get props => [];
}

class <PERSON><PERSON><PERSON>tial extends DoctorState {}

class DoctorLoading extends DoctorState {}

class NearbyDoctorsLoaded extends Doctor<PERSON>tate {
  final List<Doctor> doctors;

  const NearbyDoctorsLoaded({required this.doctors});

  @override
  List<Object?> get props => [doctors];
}

class DoctorDetailsLoaded extends DoctorState {
  final Doctor doctor;

  const DoctorDetailsLoaded({required this.doctor});

  @override
  List<Object?> get props => [doctor];
}

class DoctorAvailabilityLoaded extends DoctorState {
  final DoctorAvailability availability;

  const DoctorAvailabilityLoaded({required this.availability});

  @override
  List<Object?> get props => [availability];
}

class FilteredDoctorsLoaded extends DoctorState {
  final List<Doctor> doctors;
  final String? specialty;
  final double? minRating;
  final bool? isAvailableForSchedule;
  final String? searchQuery;

  const FilteredDoctorsLoaded({
    required this.doctors,
    this.specialty,
    this.minRating,
    this.isAvailableForSchedule,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    doctors,
    specialty,
    minRating,
    isAvailableForSchedule,
    searchQuery,
  ];
}

class DoctorSpecialtiesLoaded extends DoctorState {
  final List<String> specialties;

  const DoctorSpecialtiesLoaded({required this.specialties});

  @override
  List<Object?> get props => [specialties];
}

class DoctorError extends DoctorState {
  final String message;

  const DoctorError({required this.message});

  @override
  List<Object?> get props => [message];
}
