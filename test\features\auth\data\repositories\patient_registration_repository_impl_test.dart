import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_request_model.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_response_model.dart';
import 'package:imed_fe/features/auth/data/repositories/patient_registration_repository_impl.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/data/datasources/patient_registration_remote_data_source.dart';
import 'package:imed_fe/core/network/network_info.dart';

import '../../../auth/mocks.dart';

class MockRemoteDataSource extends Mock
    implements PatientRegistrationRemoteDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

void main() {
  late MockRemoteDataSource mockRemoteDataSource;
  late MockNetworkInfo mockNetworkInfo;
  late PatientRegistrationRepositoryImpl repository;

  setUp(() {
    mockRemoteDataSource = MockRemoteDataSource();
    mockNetworkInfo = MockNetworkInfo();
    repository = PatientRegistrationRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      networkInfo: mockNetworkInfo,
    );

    registerMocks();
  });

  final tRequest = PatientRegistrationRequest(
    roleId: 'role1',
    firstName: 'John',
    lastName: 'Doe',
    phone: '**********',
    dateOfBirth: '1990-01-01',
    gender: 'male',
  );

  final tResponseModel = PatientRegistrationResponseModel(
    id: 'id123',
    message: 'created',
    status: 'success',
    statusCode: 201,
  );

  test(
    'should return Right(response) when network connected and remote succeeds',
    () async {
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(
        () => mockRemoteDataSource.registerPatient(any()),
      ).thenAnswer((_) async => tResponseModel);

      final result = await repository.registerPatient(tRequest);

      expect(result, Right(tResponseModel));
      verify(() => mockRemoteDataSource.registerPatient(any()));
    },
  );

  test(
    'should return Left(ServerFailure) when remote throws ServerException',
    () async {
      when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
      when(
        () => mockRemoteDataSource.registerPatient(any()),
      ).thenThrow(ServerException());

      final result = await repository.registerPatient(tRequest);

      expect(result, Left(ServerFailure()));
      verify(() => mockRemoteDataSource.registerPatient(any()));
    },
  );

  test('should return Left(NetworkFailure) when not connected', () async {
    when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

    final result = await repository.registerPatient(tRequest);

    expect(result, Left(NetworkFailure()));
    verifyNever(() => mockRemoteDataSource.registerPatient(any()));
  });
}
