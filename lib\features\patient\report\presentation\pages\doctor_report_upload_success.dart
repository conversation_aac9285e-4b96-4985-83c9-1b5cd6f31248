import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class DoctorReportUploadSuccess extends StatelessWidget {
  const DoctorReportUploadSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: AppConstants.primaryColor,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: AppConstants.primaryColor,
      appBar: ReusableAppBar(
        title: Container(),
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
            color: Colors.white,
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            _SuccessMessage(),
            SizedBox(height: 120),
            _NavigationButtons(),
          ],
        ),
      ),
    );
  }
}

class _SuccessMessage extends StatelessWidget {
  const _SuccessMessage();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ReusableSvgImage(assetPath: "assets/icons/patient/success_icon.svg"),
          SizedBox(height: AppConstants.sizedBoxHeightMedium),
          Text(
            "Great !",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          Text(
            "Your report is uploaded",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class _NavigationButtons extends StatelessWidget {
  const _NavigationButtons();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          CustomOutlinedButton(
            text: "Back to dashboard",
            width: double.infinity,
            borderRadius: AppConstants.borderRadiusSmall,
            fontWeight: FontWeight.w700,
            foregroundColor: Colors.white,
            borderColor: Colors.white,
            onPressed: () {
              context.go(AppRouter.register);
            },
          ),
        ],
      ),
    );
  }
}
