import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/search_bar.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/rating.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/profile_pic.dart';

class DoctorAppointmentHistoryPage extends StatelessWidget {
  const DoctorAppointmentHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    List doctors = [
      {
        'name': 'Dr <PERSON><PERSON><PERSON>',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person1.png',
        'rating': 4.5,
        'reviewCount': 375,
        'appointment_on': '12:21 PM | 21 May 2025',
      },
      {
        'name': 'Dr <PERSON><PERSON>',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person2.png',
        'rating': 4.9,
        'reviewCount': 375,
        'appointment_on': '12:21 PM | 21 May 2025',
      },
      {
        'name': 'Dr Frances Swann',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person3.png',
        'rating': 4.9,
        'reviewCount': 375,
        'appointment_on': '12:21 PM | 21 May 2025',
      },
      {
        'name': 'Dr Kurt Bates',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person4.png',
        'rating': 4.9,
        'reviewCount': 375,
        'appointment_on': '12:21 PM | 21 May 2025',
      },
    ];
    return Scaffold(
      appBar: ReusableAppBar(
        leading: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/left_arrow.svg',
          ),
        ),
        title: Text(
          "Doctors",
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
        ),
        trailing: IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: 'assets/icons/common/bell_icon.svg',
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
              ),
              child: Column(
                children: [
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  CustomSearchBar(),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  _FilterBox(),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  _DoctorList(doctorsList: doctors),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FilterBox extends StatelessWidget {
  const _FilterBox();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.paddingSmall,
        ),
        child: Row(
          children: [
            CustomOutlinedButton(
              text: 'Upcoming',
              foregroundColor: AppConstants.primaryColor,
              borderColor: AppConstants.primaryColor,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(text: 'Previous'),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            CustomOutlinedButton(text: 'Saved for later'),
          ],
        ),
      ),
    );
  }
}

class _DoctorList extends StatelessWidget {
  final List doctorsList;
  const _DoctorList({required this.doctorsList});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: doctorsList.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            context.go(AppRouter.payment);
          },
          child: _DoctorListItem(
            name: doctorsList[index]['name'],
            specialty: doctorsList[index]['specialty'],
            imageUrl: doctorsList[index]['imageUrl'],
            rating: doctorsList[index]['rating'],
            reviewCount: doctorsList[index]['reviewCount'],
            appointmentOn: doctorsList[index]['appointment_on'],
          ),
        );
      },
    );
  }
}

class _DoctorListItem extends StatelessWidget {
  final String name;
  final String specialty;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String appointmentOn;
  const _DoctorListItem({
    required this.name,
    required this.specialty,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.appointmentOn,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ProfilePic(
                imageUrl: imageUrl,
                width: 80,
                height: 80,
                borderRadius: AppConstants.borderRadiusSmall,
              ),
              SizedBox(width: AppConstants.sizedBoxHeightMedium),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    Text(
                      specialty,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.w400,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    SizedBox(height: AppConstants.sizedBoxHeightSmall),
                    Rating(
                      rating: rating,
                      reviewCount: reviewCount,
                      iconColor: AppConstants.primaryColor,
                      countAbout: "reviewed",
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppConstants.sizedBoxHeightMedium),
              ReusableSvgImage(
                assetPath: 'assets/icons/common/right_arrow.svg',
              ),
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
            ],
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Row(
            children: [
              Row(
                children: [
                  ReusableSvgImage(
                    assetPath: 'assets/icons/patient/calendar.svg',
                    height: 16,
                    width: 12,
                    color: AppConstants.textMidColor,
                  ),
                  const SizedBox(width: AppConstants.sizedBoxHeightSmall),
                  Text(
                    "Appointment on : ",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppConstants.textMidColor,
                    ),
                  ),
                  Text(
                    appointmentOn,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppConstants.textMidColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
