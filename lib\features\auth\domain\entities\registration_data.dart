import 'package:equatable/equatable.dart';

class RegistrationData extends Equatable {
  final String fullName;
  final String email;
  final String password;
  final String? phoneNumber;
  final String? dateOfBirth;
  final String? gender;

  const RegistrationData({
    required this.fullName,
    required this.email,
    required this.password,
    this.phoneNumber,
    this.dateOfBirth,
    this.gender,
  });

  @override
  List<Object?> get props => [
        fullName,
        email,
        password,
        phoneNumber,
        dateOfBirth,
        gender,
      ];
}