import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class ServiceGridItem extends StatelessWidget {
  final String iconAsset;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback? onTap;

  const ServiceGridItem({
    super.key,
    required this.iconAsset,
    required this.title,
    required this.subtitle,
    this.color = const Color(0xFFFEFEFF),
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ReusableSvgImage(assetPath: iconAsset),
          const Gap(8),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          const Gap(2),
          Text(
            subtitle,
            style: const TextStyle(fontSize: 12, color: Color(0xFF6C6E74)),
          ),
        ],
      ),
    );
  }
}
