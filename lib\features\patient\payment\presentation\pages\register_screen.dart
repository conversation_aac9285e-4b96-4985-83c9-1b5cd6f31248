import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/profile_pic.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/rating.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';

import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/patient/appointments/presentation/widgets/search_bar.dart';

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    List doctors = [
      {
        'name': 'Dr <PERSON><PERSON><PERSON>',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person1.png',
        'rating': 4.9,
        'reviewCount': 375,
        'location': 'NMC Specialty Hospital Abu Dhabi',
        'distance': '0.9 KM away',
        'availability': 'Available for schedule',
      },
      {
        'name': 'Dr <PERSON><PERSON>',
        'specialty': 'General <PERSON>ysician',
        'imageUrl': 'assets/images/person2.png',
        'rating': 4.9,
        'reviewCount': 375,
        'location': 'NMC Specialty Hospital Abu Dhabi',
        'distance': '0.9 KM away',
        'availability': 'Available for schedule',
      },
      {
        'name': 'Dr Frances Swann',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person3.png',
        'rating': 4.9,
        'reviewCount': 375,
        'location': 'NMC Specialty Hospital Abu Dhabi',
        'distance': '0.9 KM away',
        'availability': 'Available for schedule',
      },
      {
        'name': 'Dr Kurt Bates',
        'specialty': 'General Physician',
        'imageUrl': 'assets/images/person4.png',
        'rating': 4.9,
        'reviewCount': 375,
        'location': 'NMC Specialty Hospital Abu Dhabi',
        'distance': '0.9 KM away',
        'availability': 'Available for schedule',
      },
    ];
    return Scaffold(
      appBar: ReusableAppBar(title: Text("Search")),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
            ),
            child: Column(
              children: [
                SizedBox(height: AppConstants.sizedBoxHeightMedium),
                _CategoryIconsRow(),
                SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
                CustomSearchBar(hintText: "Search doctors, pharmacies or labs"),
                SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
                _ConsultantsListView(consultantsList: doctors),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _CategoryIconsRow extends StatelessWidget {
  const _CategoryIconsRow();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.push(AppRouter.notification);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ReusableSvgImage(
            assetPath: 'assets/icons/patient/doctor_investigation_icon.svg',
            width: 56,
            height: 56,
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
          ReusableSvgImage(
            assetPath: 'assets/icons/patient/medicine_icon.svg',
            width: 56,
            height: 56,
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
          ReusableSvgImage(
            assetPath: 'assets/icons/patient/lab_report_icon.svg',
            width: 56,
            height: 56,
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
          ReusableSvgImage(
            assetPath: 'assets/icons/patient/my_condition_icon.svg',
            width: 56,
            height: 56,
          ),
        ],
      ),
    );
  }
}

class _DoctorListItem extends StatelessWidget {
  final String name;
  final String specialty;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String location;
  final String distance;
  final String availability;
  const _DoctorListItem({
    required this.name,
    required this.specialty,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.location,
    required this.distance,
    required this.availability,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ProfilePic(imageUrl: imageUrl, width: 80, height: 80),
            SizedBox(width: AppConstants.sizedBoxHeightMedium),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppConstants.textHighColor,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Text(
                    specialty,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                  SizedBox(height: AppConstants.sizedBoxHeightSmall),
                  Rating(
                    rating: rating,
                    reviewCount: reviewCount,
                    countAbout: "reviewed",
                    iconColor: AppConstants.primaryColor,
                  ),
                ],
              ),
            ),
            SizedBox(height: AppConstants.sizedBoxHeightMedium),
            IconButton(
              onPressed: () {},
              icon: ReusableSvgImage(
                assetPath: 'assets/icons/common/right_arrow.svg',
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Row(
          children: [
            ReusableSvgImage(
              assetPath: 'assets/icons/patient/hospital.svg',
              width: 10.67,
              height: 12,
              color: AppConstants.textMidColor,
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            Text(
              location,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppConstants.textMidColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Row(
          children: [
            Row(
              children: [
                ReusableSvgImage(
                  assetPath: 'assets/icons/patient/map.svg',
                  width: 10.67,
                  height: 12,
                  color: AppConstants.textMidColor,
                ),
                const SizedBox(width: AppConstants.sizedBoxHeightSmall),
                Text(
                  distance,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppConstants.textMidColor,
                  ),
                ),
              ],
            ),
            const SizedBox(width: AppConstants.sizedBoxHeightMedium),
            Row(
              children: [
                ReusableSvgImage(
                  assetPath: 'assets/icons/patient/calendar.svg',
                  width: 10.67,
                  height: 12,
                  color: AppConstants.textMidColor,
                ),
                const SizedBox(width: AppConstants.sizedBoxHeightSmall),
                Text(
                  availability,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppConstants.textMidColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),
      ],
    );
  }
}

class _ConsultantsListView extends StatelessWidget {
  final List consultantsList;
  const _ConsultantsListView({required this.consultantsList});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: consultantsList.length,
      itemBuilder: (context, index) {
        return _DoctorListItem(
          name: consultantsList[index]['name'],
          specialty: consultantsList[index]['specialty'],
          imageUrl: consultantsList[index]['imageUrl'],
          rating: consultantsList[index]['rating'],
          reviewCount: consultantsList[index]['reviewCount'],
          location: consultantsList[index]['location'],
          distance: consultantsList[index]['distance'],
          availability: consultantsList[index]['availability'],
        );
      },
    );
  }
}
