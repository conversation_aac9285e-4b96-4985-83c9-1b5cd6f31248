import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/domain/entities/auth_credentials.dart';
import 'package:imed_fe/features/auth/domain/entities/registration_data.dart';
import 'package:imed_fe/features/auth/domain/entities/user.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> signIn(AuthCredentials credentials);
  Future<Either<Failure, User>> signUp(RegistrationData registrationData);
  Future<Either<Failure, void>> signOut();
  Future<Either<Failure, void>> resetPassword(String email);
  Future<Either<Failure, void>> confirmResetPassword({
    required String token,
    required String newPassword,
  });
  Future<Either<Failure, User?>> getCurrentUser();
}