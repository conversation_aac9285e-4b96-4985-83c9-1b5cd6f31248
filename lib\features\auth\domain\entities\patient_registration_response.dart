import 'package:equatable/equatable.dart';

class PatientRegistrationResponse extends Equatable {
  final String id;
  final String message;
  final String status;
  final int statusCode;

  const PatientRegistrationResponse({
    required this.id,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object> get props => [
        id,
        message,
        status,
        statusCode,
      ];
}
