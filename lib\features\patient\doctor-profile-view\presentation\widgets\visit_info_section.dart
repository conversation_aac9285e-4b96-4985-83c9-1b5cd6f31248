import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/widgets/custom_elevated_button.dart';
import '../pages/doctor_schedule.dart';

class VisitInfoSection extends StatelessWidget {
  const VisitInfoSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Average consultation time',
            style: TextStyle(
                color: AppConstants.textMidColor,
                fontSize: AppConstants.fontSizeSmall),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall - 4),
          const Text(
            '10-15 Minutes',
            style: TextStyle(
                fontSize: AppConstants.fontSizeSmall + 3,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightLarge),
          const Text(
            'Available for consultation',
            style: TextStyle(
                fontSize: AppConstants.fontSizeSmall + 3,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
          _buildSlotCard(
            context: context,
            day: 'Monday - Friday',
            time: '8:00 AM - 6:00 AM',
            availability: '4 Slots available this week',
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
          _buildSlotCard(
            context: context,
            day: 'Saturday',
            time: '12:30 PM',
            availability: '1 Slots available this week',
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightLarge),
          _buildLocationSection(),
          const SizedBox(height: AppConstants.sizedBoxHeightLarge),
          const Text(
            'This doctor_profile_view accepts',
            style: TextStyle(
                fontSize: AppConstants.fontSizeSmall + 3,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
          Row(
            children: [
              _insuranceLogo('assets/images/metlife.png'),
              const SizedBox(width: AppConstants.paddingSmall + 2),
              _insuranceLogo('assets/images/phoenix.png'),
              const SizedBox(width: AppConstants.paddingSmall + 2),
              _insuranceLogo('assets/images/karnaphuli.png'),
            ],
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightLarge),
          const Text(
            'Do you have insurance ?',
            style: TextStyle(
                fontSize: AppConstants.fontSizeSmall + 3,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall - 2),
          const Text(
            'Using insurance can reduce your consultation costs. Add your insurance info now or later in your profile.',
            style: TextStyle(
                fontSize: AppConstants.fontSizeSmall + 1.5,
                color: AppConstants.textMidColor),
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Color(0xFFD1D5DB)),
                    foregroundColor: AppConstants.textPrimaryColor,
                    padding: const EdgeInsets.symmetric(
                        vertical: AppConstants.paddingMedium - 2),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                      BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                  ),
                  child: const Text(
                    "Non Added",
                    style: TextStyle(fontSize: AppConstants.fontSizeSmall + 2),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall + 4),
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {},
                  text: "Add Insurance",
                  backgroundColor: AppConstants.backgroundColor,
                  foregroundColor: AppConstants.primaryColor,
                  borderRadius: AppConstants.borderRadiusSmall,
                  fontSize: AppConstants.fontSizeSmall + 2,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.sizedBoxHeightLarge),
        ],
      ),
    );
  }

  Widget _buildSlotCard({
    required BuildContext context,
    required String day,
    required String time,
    required String availability,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingSmall + 4),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius:
        BorderRadius.circular(AppConstants.borderRadiusSmall + 2),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.calendar_today,
              size: AppConstants.fontSizeLarge, color: AppConstants.textMidColor),
          const SizedBox(width: AppConstants.sizedBoxHeightSmall + 4),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(day,
                    style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall + 2,
                        fontWeight: FontWeight.w600)),
                const SizedBox(height: AppConstants.sizedBoxHeightSmall - 6),
                Text(time,
                    style:
                    const TextStyle(fontSize: AppConstants.fontSizeSmall + 1.5)),
                const SizedBox(height: AppConstants.sizedBoxHeightSmall - 6),
                Text(
                  availability,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall + 0.5,
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: CustomElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => DoctorSchedulePage()),
                );
              },
              text: "Book",
              backgroundColor: AppConstants.elevatedButtonForegroundColor,
              foregroundColor: AppConstants.primaryColor,
              fontSize: AppConstants.fontSizeSmall + 2,
              fontWeight: FontWeight.w500,
              borderRadius: AppConstants.borderRadiusSmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingSmall + 4),
      decoration: BoxDecoration(
        borderRadius:
        BorderRadius.circular(AppConstants.borderRadiusSmall + 2),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          const SizedBox(width: AppConstants.paddingSmall + 4),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'NMC Specialty Hospital Abu Dhabi',
                  style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall + 2.5,
                      fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: AppConstants.sizedBoxHeightSmall),
                SizedBox(
                  width: double.infinity,
                  child: CustomElevatedButton(
                    onPressed: () {},
                    text: "View location",
                    backgroundColor: AppConstants.inputFieldBackgroundColor,
                    foregroundColor: AppConstants.primaryColor,
                    borderRadius: AppConstants.borderRadiusSmall - 7,
                    fontSize: AppConstants.fontSizeSmall + 2,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
          const SizedBox(width: AppConstants.paddingExtraLarge - 2),
          ClipRRect(
            borderRadius:
            BorderRadius.circular(AppConstants.borderRadiusSmall - 2),
            child: Image.asset(
              'assets/images/hospital_map.png',
              width: 80,
              height: 80,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }

  Widget _insuranceLogo(String path) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      child: Image.asset(
        path,
        width: 50,
        height: 50,
        fit: BoxFit.cover,
      ),
    );
  }
}
