import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/auth/data/models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signIn(String email, String password);
  Future<UserModel> signUp(String fullName, String email, String password, String? phoneNumber, String? dateOfBirth, String? gender);
  Future<void> signOut();
  Future<void> resetPassword(String email);
  Future<void> confirmResetPassword({
    required String token,
    required String newPassword,
  });
  Future<UserModel?> getCurrentUser();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioClient dioClient;

  AuthRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<UserModel> signIn(String email, String password) async {
    try {
      final response = await dioClient.post(
        '/auth/login',
        data: {
          'email': email,
          'password': password,
        },
      );

      return UserModel.fromJson(response.data['user']);
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<UserModel> signUp(String fullName, String email, String password, String? phoneNumber, String? dateOfBirth, String? gender) async {
    try {
      final response = await dioClient.post(
        '/auth/register',
        data: {
          'name': fullName,
          'email': email,
          'password': password,
          'phone_number': phoneNumber,
          'date_of_birth': dateOfBirth,
          'gender': gender,
        },
      );

      return UserModel.fromJson(response.data['user']);
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await dioClient.post('/auth/logout');
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await dioClient.post(
        '/auth/reset-password',
        data: {
          'email': email,
        },
      );
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<void> confirmResetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      await dioClient.post(
        '/auth/confirm-reset-password',
        data: {
          'token': token,
          'password': newPassword,
        },
      );
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final response = await dioClient.get('/auth/user');
      return UserModel.fromJson(response.data['user']);
    } catch (e) {
      return null;
    }
  }
}