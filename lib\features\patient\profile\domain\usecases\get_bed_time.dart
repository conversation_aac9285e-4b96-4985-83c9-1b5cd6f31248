import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/patient/profile/domain/entities/bed_time.dart';
import 'package:imed_fe/features/patient/profile/domain/repositories/home_repository.dart';

class GetBedTime implements UseCase<BedTime, NoParams> {
  final HomeRepository repository;

  GetBedTime(this.repository);

  @override
  Future<Either<Failure, BedTime>> call(NoParams params) async {
    try {
      final result = await repository.getBedTime();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
