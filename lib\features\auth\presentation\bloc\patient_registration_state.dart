part of 'patient_registration_bloc.dart';

abstract class PatientRegistrationState extends Equatable {
  const PatientRegistrationState();
  
  @override
  List<Object?> get props => [];
}

class PatientRegistrationInitial extends PatientRegistrationState {}

class PatientRegistrationLoading extends PatientRegistrationState {}

class PatientRegistrationSuc<PERSON> extends PatientRegistrationState {
  final PatientRegistrationResponse response;

  const PatientRegistrationSuccess({required this.response});

  @override
  List<Object?> get props => [response];
}

class PatientRegistrationError extends PatientRegistrationState {
  final String message;

  const PatientRegistrationError({required this.message});

  @override
  List<Object?> get props => [message];
}
