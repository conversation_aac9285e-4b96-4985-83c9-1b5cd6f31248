import 'package:flutter/material.dart';
import 'package:imed_fe/core/di/injection_container.dart' as di;

import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/theme/app_theme.dart';

void main() {
  di.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'iMet',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
    );
  }
}
