import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/core/di/injection_container.dart' as di;
// import 'package:imed_fe/core/network/dio_client.dart';
// import 'package:imed_fe/core/network/network_info_impl.dart';

import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/theme/app_theme.dart';
// import 'package:imed_fe/features/auth/data/datasources/patient_registration_remote_data_source.dart';
// import 'package:imed_fe/features/auth/data/repositories/patient_registration_repository_impl.dart';
// import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';
// import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';
// import 'package:imed_fe/features/auth/presentation/bloc/patient_registration_bloc.dart';
// import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

void main() {
  di.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return
    //  BlocProvider(
    //   create:
    //       (context) => PatientRegistrationBloc(
    //         registerPatient: RegisterPatient(
    //           PatientRegistrationRepositoryImpl(
    //             remoteDataSource: PatientRegistrationRemoteDataSourceImpl(
    //               dioClient: DioClient(),
    //             ),
    //             networkInfo: NetworkInfoImpl(InternetConnection()),
    //           ),
    //         ),
    //       ),
    //   child:
    MaterialApp.router(
      title: 'iMet',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
      // ),
    );
  }
}
