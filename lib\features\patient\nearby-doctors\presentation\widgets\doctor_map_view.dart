import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../domain/entities/doctor_location.dart';

class DoctorMapView extends StatefulWidget {
  final List<DoctorLocation> doctorLocations;
  final LatLng userLocation;
  final double radius;
  final Function(DoctorLocation) onDoctorSelected;

  const DoctorMapView({
    super.key,
    required this.doctorLocations,
    required this.userLocation,
    required this.radius,
    required this.onDoctorSelected,
  });

  @override
  State<DoctorMapView> createState() => _DoctorMapViewState();
}

class _DoctorMapViewState extends State<DoctorMapView> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final Set<Circle> _circles = {};

  @override
  void initState() {
    super.initState();
    _updateMarkers();
  }

  @override
  void didUpdateWidget(DoctorMapView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.doctorLocations != widget.doctorLocations ||
        oldWidget.userLocation != widget.userLocation ||
        oldWidget.radius != widget.radius) {
      _updateMarkers();
    }
  }

  void _updateMarkers() {
    _markers.clear();
    _circles.clear();

    // Add user location marker
    _markers.add(
      Marker(
        markerId: const MarkerId('user_location'),
        position: widget.userLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: const InfoWindow(title: 'Your Location'),
      ),
    );

    // Add search radius circle
    _circles.add(
      Circle(
        circleId: const CircleId('search_radius'),
        center: widget.userLocation,
        radius: widget.radius * 1000, // Convert km to meters
        fillColor: Colors.blue.withAlpha(25),
        strokeColor: Colors.blue,
        strokeWidth: 1,
      ),
    );

    // Add doctor location markers
    for (var i = 0; i < widget.doctorLocations.length; i++) {
      final doctor = widget.doctorLocations[i];
      _markers.add(
        Marker(
          markerId: MarkerId('doctor_${doctor.id}'),
          position: LatLng(doctor.latitude, doctor.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueAzure,
          ),
          infoWindow: InfoWindow(
            title: doctor.name,
            snippet: '${doctor.distance.toStringAsFixed(1)} km away',
            onTap: () => widget.onDoctorSelected(doctor),
          ),
        ),
      );
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: GoogleMap(
        initialCameraPosition: CameraPosition(
          target: widget.userLocation,
          zoom: 13,
        ),
        markers: _markers,
        circles: _circles,
        myLocationEnabled: true,
        myLocationButtonEnabled: true,
        compassEnabled: true,
        onMapCreated: (controller) {
          _mapController = controller;
          _fitBounds();
        },
      ),
    );
  }

  void _fitBounds() {
    if (_mapController == null || widget.doctorLocations.isEmpty) return;

    final bounds = _calculateBounds();
    _mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
  }

  LatLngBounds _calculateBounds() {
    double minLat = widget.userLocation.latitude;
    double maxLat = widget.userLocation.latitude;
    double minLng = widget.userLocation.longitude;
    double maxLng = widget.userLocation.longitude;

    for (final doctor in widget.doctorLocations) {
      if (doctor.latitude < minLat) minLat = doctor.latitude;
      if (doctor.latitude > maxLat) maxLat = doctor.latitude;
      if (doctor.longitude < minLng) minLng = doctor.longitude;
      if (doctor.longitude > maxLng) maxLng = doctor.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }
}
