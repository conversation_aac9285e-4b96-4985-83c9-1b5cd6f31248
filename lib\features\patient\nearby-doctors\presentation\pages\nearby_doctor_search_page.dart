import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/bloc/doctor_bloc.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_marker_generator.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_search_filter_section.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_search_map.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/doctor_type_bottom_sheet.dart';
import 'package:imed_fe/features/patient/nearby-doctors/presentation/widgets/radius_bottom_sheet.dart';
import 'package:imed_fe/features/patient/nearby-doctors/data/models/doctor_type.dart';

class NearbyDoctorSearchPage extends StatefulWidget {
  const NearbyDoctorSearchPage({super.key});

  @override
  State<NearbyDoctorSearchPage> createState() => _NearbyDoctorSearchPageState();
}

class _NearbyDoctorSearchPageState extends State<NearbyDoctorSearchPage> {
  String? selectedDoctorType;
  double searchRadius = 500;

  // Default location (Dhaka, Bangladesh)
  static const LatLng _defaultLocation = LatLng(23.8103, 90.4125);

  // Google Maps controller
  final Completer<GoogleMapController> _controller = Completer();

  // Add a Set to store markers
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    // Load doctor specialties when the page is initialized
    context.read<DoctorBloc>().add(const LoadDoctorSpecialties());
    // Initialize markers
    _initializeMarkers();
  }

  Future<void> _initializeMarkers() async {
    // Create user marker
    final userMarker = DoctorMarkerGenerator.generateUserMarker(
      _defaultLocation,
    );

    // Generate doctor markers
    final doctorMarkers = await DoctorMarkerGenerator.generateDoctorMarkers(
      _defaultLocation,
    );

    setState(() {
      _markers
        ..clear()
        ..add(userMarker)
        ..addAll(doctorMarkers);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Doctors near by'),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Map view (takes most of the screen)
            Container(
              height: MediaQuery.of(context).size.height * 0.6,
              child: Stack(
                children: [
                  // Google Map using reusable component
                  DoctorSearchMap(
                    initialLocation: _defaultLocation,
                    markers: _markers, // Pass the stored markers
                    onMapCreated: (controller) {
                      _controller.complete(controller);
                    },
                  ),
                ],
              ),
            ),
            Gap(40),
            // Search bar section),

            // Bottom filter section using reusable component
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 16,
              ),
              child: DoctorSearchFilterSection(
                selectedDoctorType: selectedDoctorType,
                searchRadius: searchRadius,
                onDoctorTypeSelect: _showDoctorTypeBottomSheet,
                onRadiusSelect: _showRadiusBottomSheet,
                onSearch: () {
                  context.push(AppRouter.doctorsList);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDoctorTypeBottomSheet() {
    final types = [
      DoctorType(name: 'Family Doctor', assetPath: 'assets/icons/patient/family.svg', doctorCount: 8),
      DoctorType(name: 'Medicine', assetPath: 'assets/icons/patient/medicine.svg', doctorCount: 8),
      DoctorType(name: 'Dentist', assetPath: 'assets/icons/patient/dentist.svg', doctorCount: 21),
      DoctorType(name: 'Pulmologist', assetPath: 'assets/icons/patient/heart.svg', doctorCount: 19),
      DoctorType(name: 'Gastrologist', assetPath: 'assets/icons/patient/stomach.svg', doctorCount: 8),
      DoctorType(name: 'Cardiologist', assetPath: 'assets/icons/patient/lungs.svg', doctorCount: 8),
      DoctorType(name: 'Optomologist', assetPath: 'assets/icons/patient/heart.svg', doctorCount: 8),
    ];

    // Use the reusable DoctorTypeBottomSheet component
    DoctorTypeBottomSheet.show(
      context: context,
      selectedDoctorType: selectedDoctorType,
      onTypeSelected: (type) {
        setState(() {
          selectedDoctorType = type;
        });
      },
    );
  }

  void _showRadiusBottomSheet() {
    // Use the reusable RadiusBottomSheet component
    RadiusBottomSheet.show(
      context: context,
      selectedRadius: searchRadius,
      onRadiusSelected: (radius) {
        setState(() {
          searchRadius = radius;
        });
      },
      radiusOptions: const [500, 1000, 2000, 5000],
    );
  }
}
