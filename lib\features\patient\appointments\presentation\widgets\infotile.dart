import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class InfoTile extends StatelessWidget {
  final bool showIcon;
  final String title;
  final String subtitle;
  final String? iconPath;
  final Color iconColor;
  final Color titleColor;
  final Color subtitleColor;
  const InfoTile({
    super.key,
    this.showIcon = false,
    required this.title,
    required this.subtitle,
    this.iconPath,
    this.iconColor = const Color(0xFF2260FF),
    this.titleColor = const Color(0xFF6C6E74),
    this.subtitleColor = const Color(0xFF3E3E4C),
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        (showIcon)
            ? ReusableSvgImage(
              assetPath: iconPath ?? 'assets/icons/info_icon.svg',
              color: iconColor,
            )
            : const SizedBox.shrink(),
        const SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.w400,
                color: titleColor,
              ),
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: subtitleColor,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
