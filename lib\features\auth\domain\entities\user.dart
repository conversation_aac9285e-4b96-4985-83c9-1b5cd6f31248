import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? phoneNumber;
  final String? dateOfBirth;
  final String? gender;
  final String? profilePicture;

  const User({
    required this.id,
    required this.email,
    this.name,
    this.phoneNumber,
    this.dateOfBirth,
    this.gender,
    this.profilePicture,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        phoneNumber,
        dateOfBirth,
        gender,
        profilePicture,
      ];
}