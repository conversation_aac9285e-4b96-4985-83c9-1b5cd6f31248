import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import '../../../../../core/constants/app_constants.dart';

class DoctorPatientCallingPage extends StatelessWidget {
  const DoctorPatientCallingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.textPrimaryColor,
      body: GestureDetector(
        onTap: () => context.push(AppRouter.uploadingPrescription),
        child: Stack(
          children: [
            Positioned.fill(
              child: Image.asset(
                'assets/images/doctor_pic.png',
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              top: AppConstants.paddingExtraLarge,
              right: AppConstants.paddingMedium,
              child: IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.camera_alt_outlined,
                  color: AppConstants.elevatedButtonForegroundColor,
                ),
              ),
            ),
            Positioned(
              bottom:
                  AppConstants.paddingExtraLarge * 3 +
                  AppConstants.paddingMedium,
              right: AppConstants.paddingMedium + AppConstants.paddingSmall,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusSmall,
                ),
                child: Container(
                  width: 100,
                  height: 130,
                  color: AppConstants.borderColor2,
                  child: Image.asset(
                    'assets/images/patient_pic.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.paddingMedium,
                ),
                color: AppConstants.textPrimaryColor.withOpacity(0.4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildControlButton(Icons.mic_none, "Mute"),
                    _buildControlButton(
                      Icons.call_end,
                      "End Call",
                      background: AppConstants.errorColor,
                    ),
                    _buildControlButton(Icons.more_horiz, "More"),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton(IconData icon, String label, {Color? background}) {
    return Column(
      children: [
        FloatingActionButton(
          heroTag: label,
          onPressed: () {},
          backgroundColor: background ?? AppConstants.textHighColor,
          child: Icon(icon, color: AppConstants.elevatedButtonForegroundColor),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Text(
          label,
          style: const TextStyle(
            color: AppConstants.elevatedButtonForegroundColor,
            fontSize: AppConstants.fontSizeSmall,
          ),
        ),
      ],
    );
  }
}
