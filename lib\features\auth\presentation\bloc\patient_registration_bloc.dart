import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';
import 'package:imed_fe/features/auth/domain/usecases/register_patient.dart';

part 'patient_registration_event.dart';
part 'patient_registration_state.dart';

class PatientRegistrationBloc
    extends Bloc<PatientRegistrationEvent, PatientRegistrationState> {
  final RegisterPatient registerPatient;

  PatientRegistrationBloc({required this.registerPatient})
    : super(PatientRegistrationInitial()) {
    on<RegisterPatientRequested>(_onRegisterPatientRequested);
  }

  Future<void> _onRegisterPatientRequested(
    RegisterPatientRequested event,
    Emitter<PatientRegistrationState> emit,
  ) async {
    emit(PatientRegistrationLoading());

    final result = await registerPatient(event.request);

    result.fold(
      (failure) => emit(
        PatientRegistrationError(message: _mapFailureToMessage(failure)),
      ),
      (response) => emit(PatientRegistrationSuccess(response: response)),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'Server error occurred. Please try again.';
    } else if (failure is NetworkFailure) {
      return 'No internet connection. Please check your network.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}
