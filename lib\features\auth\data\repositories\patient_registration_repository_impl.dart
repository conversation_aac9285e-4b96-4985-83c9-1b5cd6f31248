import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/network/network_info.dart';
import 'package:imed_fe/features/auth/data/datasources/patient_registration_remote_data_source.dart';
import 'package:imed_fe/features/auth/data/models/patient_registration_request_model.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';
import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';

class PatientRegistrationRepositoryImpl
    implements PatientRegistrationRepository {
  final PatientRegistrationRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  PatientRegistrationRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, PatientRegistrationResponse>> registerPatient(
    PatientRegistrationRequest request,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final requestModel = PatientRegistrationRequestModel.fromEntity(
          request,
        );
        final response = await remoteDataSource.registerPatient(requestModel);
        return Right(response);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
