import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class DoctorSearchMap extends StatelessWidget {
  final LatLng initialLocation;
  final Set<Marker> markers;
  final Function(GoogleMapController) onMapCreated;

  const DoctorSearchMap({
    required this.initialLocation,
    required this.markers,
    required this.onMapCreated,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: initialLocation,
        zoom: 14,
      ),
      markers: markers,
      onMapCreated: onMapCreated,
      myLocationEnabled: true,
      myLocationButtonEnabled: true,
    );
  }
}
