import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/auth/domain/entities/user_type.dart';

class UserTypeScreen extends StatelessWidget {
  const UserTypeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2B60E9), Color(0xFF56B3FF)],
          ),
        ),

        child: Safe<PERSON>rea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              children: [
                const SizedBox(height: 50),
                // Logo
                ReusableSvgImage(
                  assetPath: 'assets/images/imed_logo.svg',
                  height: 140,
                  width: 104,
                  placeholder: const Icon(
                    Icons.medical_services_outlined,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 50),
                // Title
                const Text(
                  'Tell us who you are',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                // Subtitle
                const Text(
                  'To make sure you get the best experience, let us know how you\'re joining us today',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
                const SizedBox(height: 50),
                // User type cards
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children:
                        UserType.all.map((userType) {
                          return _buildUserTypeCard(context, userType);
                        }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeCard(BuildContext context, UserType userType) {
    return GestureDetector(
      onTap: () {
        // Dispatch event to select user type
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.topRight,
              child: ReusableSvgImage(
                assetPath: userType.iconPath,
                height: 64,
                width: 64,
                placeholder: Icon(Icons.person, size: 40),
              ),
            ),
            Gap(30),
            Text(
              'I\'m a',
              style: const TextStyle(color: Color(0xFF6C6E74), fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              userType.displayName,
              style: TextStyle(
                color: const Color(0xFF2260FF),
                fontSize: 22,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
