import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor.dart';
import 'package:imed_fe/features/patient/nearby-doctors/domain/entities/doctor_availability.dart';

abstract class DoctorRepository {
  /// Get a list of doctors near a specific location
  Future<List<Doctor>> getNearbyDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
  });

  /// Get details of a specific doctor
  Future<Doctor> getDoctorDetails(String doctorId);

  /// Get availability of a specific doctor
  Future<DoctorAvailability> getDoctorAvailability(String doctorId);

  /// Filter doctors by criteria
  Future<List<Doctor>> filterDoctors({
    required double latitude,
    required double longitude,
    required double radius,
    String? specialty,
    double? minRating,
    bool? isAvailableForSchedule,
    String? searchQuery,
  });

  /// Get doctor types/specialties
  Future<List<String>> getDoctorSpecialties();
}
