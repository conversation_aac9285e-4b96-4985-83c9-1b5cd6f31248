import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/features/auth/presentation/pages/splash_page.dart';

void main() {
  testWidgets(
    'SplashPage shows Sign in and Register buttons and navigation works',
    (tester) async {
      final router = GoRouter(
        initialLocation: '/',
        routes: [
          GoRoute(path: '/', builder: (context, state) => const SplashPage()),
          GoRoute(
            path: '/signin',
            builder:
                (context, state) =>
                    const Scaffold(key: Key('signin-route'), body: SizedBox()),
          ),
          GoRoute(
            path: '/signup',
            builder:
                (context, state) =>
                    const Scaffold(key: Key('signup-route'), body: SizedBox()),
          ),
        ],
      );

      await tester.pumpWidget(MaterialApp.router(routerConfig: router));

      // Wait for the router to build
      await tester.pumpAndSettle();

      // Buttons: Sign in is an ElevatedButton, Register is a TextButton
      expect(find.widgetWithText(ElevatedButton, 'Sign in'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Register'), findsOneWidget);

      // tapping 'Sign in' should navigate to /signin
      await tester.tap(find.widgetWithText(ElevatedButton, 'Sign in'));
      await tester.pumpAndSettle();

      // The '/signin' route builds a widget with key 'signin-route'
      expect(find.byKey(const Key('signin-route')), findsOneWidget);
    },
  );
}
