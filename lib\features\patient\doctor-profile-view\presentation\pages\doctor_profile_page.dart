import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/widgets/reusable_app_bar.dart';
import '../widgets/visit_info_section.dart';

class DoctorProfilePage extends StatelessWidget {
  const DoctorProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: ReusableAppBar(
        leading: const BackButton(color: AppConstants.textPrimaryColor),
        onLeadingTap: () => Navigator.pop(context),
        title: const Text(
          'Doctor',
          style: TextStyle(
            color: AppConstants.textPrimaryColor,
            fontWeight: FontWeight.w600,
            fontSize: AppConstants.fontSizeLarge,
          ),
        ),
        trailing: Padding(
          padding: const EdgeInsets.only(right: AppConstants.paddingMedium),
          child: SvgPicture.asset(
            'assets/icons/patient/chat.svg',
            colorFilter: const ColorFilter.mode(AppConstants.textPrimaryColor, BlendMode.srcIn),
            width: 24,
            height: 24,
          ),
        ),
      ),
      bottomNavigationBar: Container(
        height: 70,
        padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium),
        decoration: BoxDecoration(
          border:
          Border(top: BorderSide(color: Colors.grey.shade300)),
          color: AppConstants.backgroundColor,
        ),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Consultation Fee',
                  style: TextStyle(fontSize: AppConstants.fontSizeSmall),
                ),
                const Text(
                  '45 AED',
                  style: TextStyle(fontSize: AppConstants.fontSizeMedium, color: AppConstants.textPrimaryColor),
                ),
              ],
            ),
            const Spacer(),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // need to navigate
                },
                icon: const Icon(Icons.calendar_month,
                    size: AppConstants.fontSizeMedium),
                label: const Text("Book Now", style: TextStyle(color: AppConstants.elevatedButtonForegroundColor),),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingLarge,
                      vertical: AppConstants.paddingMedium),
                  textStyle: const TextStyle(fontWeight: FontWeight.w600),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                    BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall + 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius:
                  BorderRadius.circular(AppConstants.borderRadiusSmall + 4),
                  child: Image.asset(
                    'assets/images/doctor_pic.png',
                    width: 70,
                    height: 80,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: AppConstants.sizedBoxHeightMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Dr Rhonda Rhodes',
                        style: TextStyle(
                            fontSize: AppConstants.fontSizeExtraLarge - 4,
                            fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(
                          height: AppConstants.sizedBoxHeightSmall - 4),
                      const Text('MBBS FCPS',
                          style: TextStyle(color: AppConstants.textMidColor)),
                      const SizedBox(
                          height: AppConstants.sizedBoxHeightSmall - 4),
                      Text(
                        'Family Doctor',
                        style: TextStyle(
                          color: AppConstants.primaryColor,
                          fontSize: AppConstants.fontSizeSmall + 2,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text('Experience',
                    style: TextStyle(color: AppConstants.textMidColor)),
                SizedBox(width: 25),
                Text('Rating',
                    style: TextStyle(color: AppConstants.textMidColor)),
              ],
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall - 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text('5 years+',
                    style: TextStyle(fontWeight: FontWeight.w500)),
                SizedBox(width: 30),
                Row(
                  children: [
                    Icon(Icons.star,
                        color: Colors.amber, size: AppConstants.fontSizeMedium),
                    SizedBox(width: AppConstants.sizedBoxHeightSmall - 4),
                    Text('4.8 / 5 (378 Reviews)',
                        style: TextStyle(fontWeight: FontWeight.w500)),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
            Text('Serving in',
                style: TextStyle(color: AppConstants.textMidColor)),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall - 6),
            const Text(
              '🏥 NMC Specialty Hospital Abu Dhabi',
              style: TextStyle(color: AppConstants.textMidColor),
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightLarge),
            DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  const TabBar(
                    labelColor: AppConstants.textPrimaryColor,
                    unselectedLabelColor: AppConstants.textPrimaryColor,
                    indicatorColor: AppConstants.textPrimaryColor,
                    labelStyle: TextStyle(fontSize: AppConstants.fontSizeSmall),
                    unselectedLabelStyle: TextStyle(fontSize: AppConstants.fontSizeSmall),
                    tabs: [
                      Tab(text: 'Visit information'),
                      Tab(text: 'Profile'),
                      Tab(text: 'Reviews'),
                    ],
                  ),
                  Container(
                    height: 500,
                    padding: const EdgeInsets.only(
                        top: AppConstants.paddingMedium + 4),
                    child: TabBarView(
                      children: [
                        VisitInfoSection(),
                        Center(child: Text('Profile')),
                        Center(child: Text('Reviews')),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
