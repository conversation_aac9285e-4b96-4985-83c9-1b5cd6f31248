import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/router/app_router.dart';

import 'package:imed_fe/core/utils/validation_utils.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/reusable_app_bar.dart';
import 'package:imed_fe/core/widgets/reusable_icon_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/auth/presentation/widgets/social_login_section.dart';

import 'package:imed_fe/core/widgets/custom_text_field.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dobController = TextEditingController();

  String? _selectedGender;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _dobController.dispose();

    super.dispose();
  }

  void _handleRegister() {
    context.push(AppRouter.patientProfile);
    if (_formKey.currentState!.validate()) {
      context.push(AppRouter.setPassword, extra: _emailController.text);
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _dobController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReusableAppBar(
        leading: ReusableSvgImage(assetPath: 'assets/icons/left.svg'),
        title: const Text('New Account'),
        onLeadingTap: () => context.push(AppRouter.signin),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Full Name",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),

                _buildNameField(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Text(
                  "Email or Mobile number",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildEmailField(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Text(
                  'Mobile number',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildPhoneField(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),
                Text(
                  'Date of birth',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildDateOfBirthField(),
                const SizedBox(height: AppConstants.sizedBoxHeightMedium),

                Text(
                  'I am a',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textMidColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                _buildGenderSelection(),

                const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                CustomElevatedButton(
                  onPressed: _handleRegister,
                  text: 'Sign up',
                ),
                const SizedBox(
                  height: AppConstants.sizedBoxHeightExtraLarge + 30,
                ),
                const SocialLoginSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return CustomTextField(
      controller: _nameController,

      hintText: 'Enter your full name',
      validator:
          (value) => ValidationUtils.validateRequired(value, 'Full name'),
    );
  }

  Widget _buildEmailField() {
    return CustomTextField(
      controller: _emailController,

      hintText: 'Enter your email or mobile number',
      keyboardType: TextInputType.emailAddress,
      validator: ValidationUtils.validateEmail,
    );
  }

  Widget _buildPhoneField() {
    return CustomTextField(
      controller: _phoneController,
      labelText: 'Mobile number',
      keyboardType: TextInputType.phone,
      prefixIcon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('🇺🇸 +1', style: TextStyle(fontSize: 16)),
                Icon(Icons.arrow_drop_down, size: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateOfBirthField() {
    return CustomTextField(
      controller: _dobController,
      labelText: 'Date of birth',
      hintText: 'dd/mm/yyyy',
      readOnly: true,
      onTap: () => _selectDate(context),
    );
  }

  Widget _buildGenderSelection() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedGender = 'Male';
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      _selectedGender == 'Male'
                          ? AppConstants.primaryColor
                          : AppConstants.textMidColor,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Male',
                  style: TextStyle(
                    color:
                        _selectedGender == 'Male'
                            ? AppConstants.primaryColor
                            : AppConstants.textPrimaryColor,
                    fontWeight:
                        _selectedGender == 'Male'
                            ? FontWeight.bold
                            : FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedGender = 'Female';
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      _selectedGender == 'Female'
                          ? Colors.blue
                          : Colors.grey.shade300,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Female',
                  style: TextStyle(
                    color:
                        _selectedGender == 'Female'
                            ? Colors.blue
                            : Colors.black,
                    fontWeight:
                        _selectedGender == 'Female'
                            ? FontWeight.bold
                            : FontWeight.normal,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
