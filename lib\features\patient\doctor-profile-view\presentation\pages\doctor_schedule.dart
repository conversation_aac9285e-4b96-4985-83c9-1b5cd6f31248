import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/widgets/reusable_app_bar.dart';

class DoctorSchedulePage extends StatelessWidget {
  const DoctorSchedulePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: ReusableAppBar(
        leading: const BackButton(color: AppConstants.textPrimaryColor),
        onLeadingTap: () => Navigator.pop(context),
        title: const Text(
          'Doctor',
          style: TextStyle(
            color: AppConstants.textPrimaryColor,
            fontSize: AppConstants.fontSizeLarge,
          ),
        ),
        trailing: const Padding(
          padding: EdgeInsets.only(right: AppConstants.paddingMedium),
          child: Icon(
            Icons.chat_bubble_outline,
            color: AppConstants.textPrimaryColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDoctorInfo(),
            const SizedBox(height: AppConstants.sizedBoxHeightLarge),
            _buildDateSelector(),
            const SizedBox(height: AppConstants.sizedBoxHeightMedium),
            const Text(
              "Select your appointment time",
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
            _buildSection("Morning", Icons.wb_sunny_outlined, const [
              "08:00 AM",
              "08:30 AM",
              "09:00 AM",
              "09:30 AM",
              "10:00 AM",
              "10:30 AM",
              "11:00 AM",
            ], selected: "10:00 AM"),
            _buildSection("Afternoon", Icons.sunny, const [
              "12:30 PM",
              "01:00 PM",
              "02:00 PM",
              "02:30 PM",
            ], selected: "12:30 PM"),
            _buildSection("Evening", Icons.wb_twilight, const [
              "05:00 PM",
              "05:30 PM",
              "06:00 PM",
              "06:30 PM",
              "07:00 PM",
              "07:30 PM",
              "08:00 PM",
            ], selected: "07:00 PM"),
            _buildSection("Night", Icons.nightlight_outlined, const [
              "09:00 PM",
              "09:30 PM",
              "10:00 PM",
              "10:30 PM",
              "11:00 PM",
              "11:30 PM",
            ]),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall + 4,
        ),
        decoration: const BoxDecoration(
          color: AppConstants.backgroundColor,
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Row(
              children: [
                Icon(Icons.calendar_month, color: AppConstants.primaryColor),
                SizedBox(width: AppConstants.sizedBoxHeightSmall),
                Text(
                  "12:30 PM\n28 April",
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.4,
              child: ElevatedButton(
                onPressed: () {
                  context.push(AppRouter.doctorInfoRate);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingLarge,
                    vertical: AppConstants.paddingMedium,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusSmall,
                    ),
                  ),
                ),
                child: const Text(
                  "Schedule Now",
                  style: TextStyle(
                    color: AppConstants.elevatedButtonForegroundColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoctorInfo() {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(
            AppConstants.borderRadiusSmall + 4,
          ),
          child: Image.asset(
            'assets/images/doctor_pic.png',
            width: 70,
            height: 80,
            fit: BoxFit.cover,
          ),
        ),
        const SizedBox(width: AppConstants.sizedBoxHeightMedium),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            Text(
              "Dr Rhonda Rhodes",
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              "MBBS FCPS",
              style: TextStyle(color: AppConstants.textMidColor),
            ),
            Text(
              "Family Doctor",
              style: TextStyle(color: AppConstants.primaryColor),
            ),
            SizedBox(height: AppConstants.sizedBoxHeightSmall - 4),
            Row(
              children: [
                Text(
                  "5 years+  ",
                  style: TextStyle(color: AppConstants.textMidColor),
                ),
                Icon(
                  Icons.star,
                  color: Colors.orange,
                  size: AppConstants.fontSizeMedium,
                ),
                Text(
                  " 4.8 / 5 (378 Reviews)",
                  style: TextStyle(color: AppConstants.textMidColor),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateSelector() {
    final days = const [
      "28 Mon",
      "29 Tue",
      "30 Wed",
      "01 Thu",
      "02 Fri",
      "03 Sat",
    ];
    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: days.length,
        itemBuilder: (context, index) {
          final day = days[index];
          final selected = day == "28 Mon";
          return Container(
            margin: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingSmall - 4,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingSmall + 4,
              vertical: AppConstants.paddingSmall + 2,
            ),
            decoration: BoxDecoration(
              color:
                  selected
                      ? AppConstants.primaryColor
                      : AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusSmall,
              ),
            ),
            child: Text(
              day,
              style: TextStyle(
                color:
                    selected
                        ? AppConstants.elevatedButtonForegroundColor
                        : AppConstants.textPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    List<String> slots, {
    String? selected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppConstants.sizedBoxHeightSmall + 4),
        Row(
          children: [
            Icon(icon, color: AppConstants.textMidColor),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            Text(
              title,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall + 3,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Wrap(
          spacing: AppConstants.paddingSmall + 2,
          runSpacing: AppConstants.paddingSmall + 2,
          children:
              slots.map((slot) {
                final isSelected = slot == selected;
                final isDisabled =
                    slot == "10:00 AM" ||
                    slot == "12:30 PM" ||
                    slot == "07:00 PM";
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingSmall + 4,
                    vertical: AppConstants.paddingSmall + 2,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isDisabled
                            ? AppConstants.backgroundColor
                            : isSelected
                            ? AppConstants.secondaryColor
                            : AppConstants.backgroundColor,
                    border: Border.all(
                      color:
                          isSelected
                              ? AppConstants.primaryColor
                              : Colors.grey.shade300,
                    ),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusSmall,
                    ),
                  ),
                  child: Text(
                    slot,
                    style: TextStyle(
                      color:
                          isSelected
                              ? AppConstants.elevatedButtonForegroundColor
                              : AppConstants.textHighColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}
