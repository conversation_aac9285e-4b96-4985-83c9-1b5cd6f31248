part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeLoaded extends HomeState {
  final UserProfile userProfile;
  final HealthScore healthScore;
  final BedTime bedTime;

  const HomeLoaded({
    required this.userProfile,
    required this.healthScore,
    required this.bedTime,
  });

  @override
  List<Object?> get props => [userProfile, healthScore, bedTime];
}

class HomeError extends HomeState {
  final String message;

  const HomeError({required this.message});

  @override
  List<Object?> get props => [message];
}
