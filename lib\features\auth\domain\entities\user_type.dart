import 'package:equatable/equatable.dart';

enum UserRole { patient, doctor, pharmacist, pathologist }

class UserType extends Equatable {
  final UserRole role;
  final String displayName;
  final String iconPath;

  const UserType({
    required this.role,
    required this.displayName,
    required this.iconPath,
  });

  @override
  List<Object?> get props => [role, displayName, iconPath];

  static const patient = UserType(
    role: UserRole.patient,
    displayName: 'Patient',
    iconPath: 'assets/images/patient.svg',
  );

  static const doctor = UserType(
    role: UserRole.doctor,
    displayName: 'Doctor',
    iconPath: 'assets/images/doctor.svg',
  );

  static const pharmacist = UserType(
    role: UserRole.pharmacist,
    displayName: 'Pharmacist',
    iconPath: 'assets/images/pharmacist.svg',
  );

  static const pathologist = UserType(
    role: UserRole.pathologist,
    displayName: 'Pathologist',
    iconPath: 'assets/images/pathologist.svg',
  );

  static List<UserType> get all => [patient, doctor, pharmacist, pathologist];
}
