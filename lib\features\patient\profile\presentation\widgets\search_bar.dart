import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class CustomSearchBar extends StatelessWidget {
  final String hintText;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final BorderRadius? borderRadius;

  const CustomSearchBar({
    super.key,
    this.hintText = 'Search doctors, pharmacies or labs',
    this.onTap,
    this.onChanged,
    this.backgroundColor = const Color(0xFFF2F5FF),
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            const Icon(Icons.search, color: Colors.grey, size: 16),
            const Gap(8),
            Expanded(
              child:
                  onChanged != null
                      ? TextField(
                        decoration: InputDecoration(
                          hintText: hintText,
                          hintStyle: const TextStyle(color: Colors.blueGrey),
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                        onChanged: onChanged,
                      )
                      : Text(
                        hintText,
                        style: const TextStyle(
                          color: Color(0xFF718DE0),
                          fontSize: 16,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
