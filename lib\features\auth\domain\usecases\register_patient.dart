import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_request.dart';
import 'package:imed_fe/features/auth/domain/entities/patient_registration_response.dart';
import 'package:imed_fe/features/auth/domain/repositories/patient_registration_repository.dart';

class RegisterPatient implements UseCase<PatientRegistrationResponse, PatientRegistrationRequest> {
  final PatientRegistrationRepository repository;

  RegisterPatient(this.repository);

  @override
  Future<Either<Failure, PatientRegistrationResponse>> call(
    PatientRegistrationRequest params,
  ) async {
    return await repository.registerPatient(params);
  }
}
