import 'package:flutter/material.dart';

class ScheduleCard extends StatelessWidget {
  final String dayRange;
  final String time;
  final String slotsAvailable;

  const ScheduleCard({
    super.key,
    required this.dayRange,
    required this.time,
    required this.slotsAvailable,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: const Color(0xFFF2F6FD),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(dayRange,
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text(time),
                  const SizedBox(height: 4),
                  Text(
                    slotsAvailable,
                    style: const TextStyle(color: Color(0xFF0059F7)),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                foregroundColor: const Color(0xFF0059F7),
                backgroundColor: Colors.white,
                side: const BorderSide(color: Color(0xFF0059F7)),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text("Book"),
            )
          ],
        ),
      ),
    );
  }
}