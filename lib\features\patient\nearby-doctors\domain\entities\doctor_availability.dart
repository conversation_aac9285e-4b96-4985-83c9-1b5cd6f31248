import 'package:equatable/equatable.dart';

class TimeSlot extends Equatable {
  final String startTime;
  final String endTime;
  final bool isAvailable;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
    this.isAvailable = true,
  });

  @override
  List<Object?> get props => [startTime, endTime, isAvailable];
}

class DaySchedule extends Equatable {
  final String day;
  final bool isAvailable;
  final List<TimeSlot> timeSlots;

  const DaySchedule({
    required this.day,
    this.isAvailable = true,
    required this.timeSlots,
  });

  @override
  List<Object?> get props => [day, isAvailable, timeSlots];
}

class DoctorAvailability extends Equatable {
  final List<DaySchedule> schedule;

  const DoctorAvailability({
    required this.schedule,
  });

  @override
  List<Object?> get props => [schedule];
}
